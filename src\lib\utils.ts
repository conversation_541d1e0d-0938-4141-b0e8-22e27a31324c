import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import { supabase } from './supabase'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Get the correct image URL for display
 * Handles both external URLs (like Unsplash) and Supabase Storage URLs
 */
export const getImageUrl = (imageUrl: string | null | undefined): string => {
  if (!imageUrl) {
    return 'https://via.placeholder.com/400x400?text=No+Image'
  }
  
  // If it's already a full URL (like Unsplash URLs), return it directly
  if (imageUrl.startsWith('http')) {
    return imageUrl
  }
  
  // If it's a file path stored in our bucket, get the public URL from Supabase
  const { data } = supabase.storage.from('pictures').getPublicUrl(imageUrl)
  return data?.publicUrl || 'https://via.placeholder.com/400x400?text=No+Image'
}
