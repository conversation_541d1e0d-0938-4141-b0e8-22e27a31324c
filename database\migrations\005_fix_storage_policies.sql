-- Fix storage policies for development
-- This allows public access to storage for development purposes

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Public Access to Pictures" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can upload to Pictures" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can update Pictures" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can delete Pictures" ON storage.objects;

-- Create more permissive policies for development
-- Allow public read access to all files in pictures bucket
CREATE POLICY "Public Access to Pictures" ON storage.objects 
FOR SELECT USING (bucket_id = 'pictures');

-- Allow public upload to pictures bucket (for development)
CREATE POLICY "Public upload to Pictures" ON storage.objects 
FOR INSERT WITH CHECK (bucket_id = 'pictures');

-- Allow public update to pictures bucket (for development)
CREATE POLICY "Public update Pictures" ON storage.objects 
FOR UPDATE USING (bucket_id = 'pictures');

-- Allow public delete from pictures bucket (for development)
CREATE POLICY "Public delete Pictures" ON storage.objects 
FOR DELETE USING (bucket_id = 'pictures');

-- Alternative: For complete public access (use only for development)
-- ALTER TABLE storage.objects DISABLE ROW LEVEL SECURITY; 