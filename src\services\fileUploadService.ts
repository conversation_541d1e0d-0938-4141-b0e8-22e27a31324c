import { supabase } from '../lib/supabase';

export class FileUploadService {
  private static BUCKET_NAME = 'pictures';

  // Upload a file to Supabase Storage
  static async uploadFile(file: File, folder: string = 'products'): Promise<string> {
    try {
      // Generate unique filename
      const fileExt = file.name.split('.').pop();
      const fileName = `${folder}/${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;

      // Upload file to Supabase Storage
      const { data, error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (error) {
        console.error('Upload error details:', error);
        throw new Error(`Upload failed: ${error.message}`);
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from(this.BUCKET_NAME)
        .getPublicUrl(fileName);

      return urlData.publicUrl;
    } catch (error) {
      console.error('File upload error:', error);
      throw new Error('Failed to upload file');
    }
  }

  // Delete a file from Supabase Storage
  static async deleteFile(fileUrl: string): Promise<void> {
    try {
      // Extract file path from URL
      const url = new URL(fileUrl);
      const pathParts = url.pathname.split('/');
      const fileName = pathParts[pathParts.length - 1];
      const folder = pathParts[pathParts.length - 2];
      const filePath = `${folder}/${fileName}`;

      const { error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .remove([filePath]);

      if (error) {
        throw new Error(`Delete failed: ${error.message}`);
      }
    } catch (error) {
      console.error('File deletion error:', error);
      throw new Error('Failed to delete file');
    }
  }

  // Check if bucket exists, create if not
  static async ensureBucketExists(): Promise<void> {
    try {
      // First, try to list buckets to check if we have access
      const { data: buckets, error: listError } = await supabase.storage.listBuckets();
      
      if (listError) {
        console.error('Error listing buckets:', listError);
        // If we can't list buckets, we might not have permission
        // Let's try to use the bucket directly and see if it works
        return;
      }

      const bucketExists = buckets?.some(bucket => bucket.name === this.BUCKET_NAME);

      if (!bucketExists) {
        console.log('Bucket does not exist, attempting to create...');
        
        // Try to create the bucket
        const { error: createError } = await supabase.storage.createBucket(this.BUCKET_NAME, {
          public: true,
          allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
          fileSizeLimit: 5242880 // 5MB
        });

        if (createError) {
          console.error('Failed to create bucket:', createError);
          // Don't throw error, just log it - the bucket might already exist or we might not have permission
          // The upload will fail later if there's a real issue
          return;
        }
        
        console.log('Bucket created successfully');
      } else {
        console.log('Bucket already exists');
      }
    } catch (error) {
      console.error('Bucket creation error:', error);
      // Don't throw error, just log it - let the upload attempt happen
      // If the bucket doesn't exist, the upload will fail with a clear error
    }
  }

  // Test if we can access the bucket
  static async testBucketAccess(): Promise<boolean> {
    try {
      const { data, error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .list('', { limit: 1 });
      
      if (error) {
        console.error('Bucket access test failed:', error);
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('Bucket access test error:', error);
      return false;
    }
  }

  // Validate file type and size
  static validateFile(file: File): { isValid: boolean; error?: string } {
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    const maxSize = 5 * 1024 * 1024; // 5MB

    if (!allowedTypes.includes(file.type)) {
      return {
        isValid: false,
        error: 'Please select a valid image file (JPEG, PNG, GIF, or WebP)'
      };
    }

    if (file.size > maxSize) {
      return {
        isValid: false,
        error: 'File size must be less than 5MB'
      };
    }

    return { isValid: true };
  }

  // Validate multiple files
  static validateFiles(files: FileList | File[]): { isValid: boolean; error?: string }[] {
    const fileArray = Array.from(files);
    return fileArray.map(file => this.validateFile(file));
  }
} 