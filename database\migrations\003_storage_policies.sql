-- Storage policies for the pictures bucket
-- Run this after creating the 'pictures' bucket in Supabase Storage

-- Enable RLS on storage.objects if not already enabled
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Allow public read access to all files in pictures bucket
CREATE POLICY "Public Access to Pictures" ON storage.objects 
FOR SELECT USING (bucket_id = 'pictures');

-- Allow authenticated users to upload files to pictures bucket
CREATE POLICY "Authenticated users can upload to Pictures" ON storage.objects 
FOR INSERT WITH CHECK (bucket_id = 'pictures' AND auth.role() = 'authenticated');

-- Allow authenticated users to update files in pictures bucket
CREATE POLICY "Authenticated users can update Pictures" ON storage.objects 
FOR UPDATE USING (bucket_id = 'pictures' AND auth.role() = 'authenticated');

-- Allow authenticated users to delete files from pictures bucket
CREATE POLICY "Authenticated users can delete Pictures" ON storage.objects 
FOR DELETE USING (bucket_id = 'pictures' AND auth.role() = 'authenticated');

-- Alternative: For development, you can disable <PERSON><PERSON> entirely (not recommended for production)
-- ALTER TABLE storage.objects DISABLE ROW LEVEL SECURITY; 