import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Search,
  Eye,
  Edit,
  Trash2,
  ChevronDown,
  ChevronUp,
  MoreVertical,
  Users,
  X,
  User,
  Mail,
  Phone,
  Calendar,
  ShoppingBag,
  DollarSign,
  MapPin,
  CreditCard,
  Package,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import AdminLayout from '../components/AdminLayout';

interface CustomerOrder {
  id: string;
  date: string;
  amount: number;
  status: 'Completed' | 'Pending' | 'Processing' | 'Shipped' | 'Cancelled';
  items: number;
}

interface CustomerAddress {
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
}

interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
  status: 'active' | 'inactive';
  joined: string;
  orders: number;
  totalSpent: number;
  address?: CustomerAddress;
  recentOrders?: CustomerOrder[];
  lastOrderDate?: string;
  averageOrderValue?: number;
  notes?: string;
}

const AdminCustomersPage: React.FC = () => {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [filteredCustomers, setFilteredCustomers] = useState<Customer[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [sortBy, setSortBy] = useState('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [isLoading, setIsLoading] = useState(true);
  const [showCustomerModal, setShowCustomerModal] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const navigate = useNavigate();

  // Mock data
  const mockCustomers: Customer[] = [
    {
      id: '1',
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '******-1234',
      status: 'active',
      joined: '2023-11-10',
      orders: 5,
      totalSpent: 1299.99,
      address: {
        street: '123 Main St',
        city: 'New York',
        state: 'NY',
        zipCode: '10001',
        country: 'USA'
      },
      recentOrders: [
        { id: '#1234', date: '2024-01-15', amount: 299.99, status: 'Completed', items: 2 },
        { id: '#1230', date: '2024-01-10', amount: 199.99, status: 'Completed', items: 1 },
        { id: '#1225', date: '2024-01-05', amount: 399.99, status: 'Completed', items: 3 }
      ],
      lastOrderDate: '2024-01-15',
      averageOrderValue: 259.99,
      notes: 'Premium customer, prefers express shipping'
    },
    {
      id: '2',
      name: 'Jane Smith',
      email: '<EMAIL>',
      phone: '******-5678',
      status: 'active',
      joined: '2023-12-01',
      orders: 3,
      totalSpent: 799.99,
      address: {
        street: '456 Oak Ave',
        city: 'Los Angeles',
        state: 'CA',
        zipCode: '90210',
        country: 'USA'
      },
      recentOrders: [
        { id: '#1235', date: '2024-01-14', amount: 199.99, status: 'Pending', items: 1 },
        { id: '#1228', date: '2024-01-08', amount: 299.99, status: 'Completed', items: 2 },
        { id: '#1220', date: '2024-01-02', amount: 299.99, status: 'Completed', items: 1 }
      ],
      lastOrderDate: '2024-01-14',
      averageOrderValue: 266.66,
      notes: 'New customer, very satisfied with products'
    },
    {
      id: '3',
      name: 'Bob Johnson',
      email: '<EMAIL>',
      phone: '******-8765',
      status: 'inactive',
      joined: '2023-10-15',
      orders: 2,
      totalSpent: 299.99,
      address: {
        street: '789 Pine St',
        city: 'Chicago',
        state: 'IL',
        zipCode: '60601',
        country: 'USA'
      },
      recentOrders: [
        { id: '#1236', date: '2024-01-13', amount: 399.99, status: 'Processing', items: 1 },
        { id: '#1215', date: '2023-12-20', amount: 299.99, status: 'Completed', items: 2 }
      ],
      lastOrderDate: '2024-01-13',
      averageOrderValue: 349.99,
      notes: 'Inactive customer, last order was 3 weeks ago'
    },
    {
      id: '4',
      name: 'Alice Brown',
      email: '<EMAIL>',
      phone: '******-4321',
      status: 'active',
      joined: '2024-01-05',
      orders: 4,
      totalSpent: 999.99,
      address: {
        street: '321 Elm St',
        city: 'Houston',
        state: 'TX',
        zipCode: '77001',
        country: 'USA'
      },
      recentOrders: [
        { id: '#1237', date: '2024-01-16', amount: 199.99, status: 'Shipped', items: 1 },
        { id: '#1232', date: '2024-01-12', amount: 299.99, status: 'Completed', items: 2 },
        { id: '#1227', date: '2024-01-08', amount: 299.99, status: 'Completed', items: 1 },
        { id: '#1222', date: '2024-01-05', amount: 199.99, status: 'Completed', items: 1 }
      ],
      lastOrderDate: '2024-01-16',
      averageOrderValue: 249.99,
      notes: 'Very active new customer, orders frequently'
    },
    {
      id: '5',
      name: 'Charlie Wilson',
      email: '<EMAIL>',
      phone: '******-2468',
      status: 'inactive',
      joined: '2023-09-20',
      orders: 1,
      totalSpent: 89.99,
      address: {
        street: '654 Maple Dr',
        city: 'Phoenix',
        state: 'AZ',
        zipCode: '85001',
        country: 'USA'
      },
      recentOrders: [
        { id: '#1210', date: '2023-12-15', amount: 89.99, status: 'Completed', items: 1 }
      ],
      lastOrderDate: '2023-12-15',
      averageOrderValue: 89.99,
      notes: 'One-time customer, no recent activity'
    }
  ];

  const statuses = ['all', 'active', 'inactive'];

  useEffect(() => {
    // Check authentication
    const isAuthenticated = localStorage.getItem('adminAuthenticated');
    if (!isAuthenticated) {
      navigate('/admin/login');
      return;
    }
    setCustomers(mockCustomers);
    setFilteredCustomers(mockCustomers);
    setIsLoading(false);
  }, [navigate]);

  useEffect(() => {
    filterAndSortCustomers();
  }, [customers, searchTerm, selectedStatus, sortBy, sortOrder]);

  const filterAndSortCustomers = () => {
    let filtered = customers.filter(customer => {
      const matchesSearch = customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        customer.phone.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus = selectedStatus === 'all' || customer.status === selectedStatus;
      return matchesSearch && matchesStatus;
    });
    filtered.sort((a, b) => {
      let aValue: any = a[sortBy as keyof Customer];
      let bValue: any = b[sortBy as keyof Customer];
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }
      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });
    setFilteredCustomers(filtered);
  };

  const handleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  const handleViewCustomer = (customer: Customer) => {
    setSelectedCustomer(customer);
    setShowCustomerModal(true);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'inactive':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getOrderStatusColor = (status: string) => {
    switch (status) {
      case 'Completed':
        return 'bg-green-100 text-green-800';
      case 'Pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'Processing':
        return 'bg-blue-100 text-blue-800';
      case 'Shipped':
        return 'bg-purple-100 text-purple-800';
      case 'Cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatPrice = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const SortableHeader = ({ field, children }: { field: string; children: React.ReactNode }) => (
    <button
      onClick={() => handleSort(field)}
      className="flex items-center space-x-1 text-xs font-medium text-gray-500 uppercase tracking-wider hover:text-gray-700"
    >
      <span>{children}</span>
      {sortBy === field && (
        sortOrder === 'asc' ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />
      )}
    </button>
  );

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="min-h-screen bg-gray-100 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading customers...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="min-h-screen bg-gray-100">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Customers</h1>
              <p className="mt-2 text-gray-600">Manage your customers and their activity</p>
            </div>
          </div>
        </div>

        {/* Success/Error Messages */}
        {message && (
          <div className={`mb-6 p-4 rounded-md flex items-center ${
            message.type === 'success'
              ? 'bg-green-50 border border-green-200 text-green-700'
              : 'bg-red-50 border border-red-200 text-red-700'
          }`}>
            {message.type === 'success' ? (
              <CheckCircle className="h-5 w-5 mr-2" />
            ) : (
              <AlertCircle className="h-5 w-5 mr-2" />
            )}
            {message.text}
          </div>
        )}

        {/* Filters and Search */}
        <div className="bg-white rounded-lg shadow mb-6 p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search customers..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            {/* Status Filter */}
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            >
              {statuses.map(status => (
                <option key={status} value={status}>
                  {status === 'all' ? 'All Status' : status.charAt(0).toUpperCase() + status.slice(1)}
                </option>
              ))}
            </select>
            {/* Results Count */}
            <div className="flex items-center justify-end text-sm text-gray-500">
              {filteredCustomers.length} of {customers.length} customers
            </div>
          </div>
        </div>

        {/* Customers Table */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left">
                    <SortableHeader field="name">Name</SortableHeader>
                  </th>
                  <th className="px-6 py-3 text-left">
                    <SortableHeader field="email">Email</SortableHeader>
                  </th>
                  <th className="px-6 py-3 text-left">
                    <SortableHeader field="phone">Phone</SortableHeader>
                  </th>
                  <th className="px-6 py-3 text-left">
                    <SortableHeader field="status">Status</SortableHeader>
                  </th>
                  <th className="px-6 py-3 text-left">
                    <SortableHeader field="joined">Joined</SortableHeader>
                  </th>
                  <th className="px-6 py-3 text-left">
                    <SortableHeader field="orders">Orders</SortableHeader>
                  </th>
                  <th className="px-6 py-3 text-left">
                    <SortableHeader field="totalSpent">Total Spent</SortableHeader>
                  </th>
                  <th className="px-6 py-3 text-right">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredCustomers.map((customer) => (
                  <tr key={customer.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-gray-100 flex items-center justify-center text-lg">
                            <Users className="h-6 w-6 text-gray-400" />
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">{customer.name}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {customer.email}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {customer.phone}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(customer.status)}`}>
                        {customer.status.charAt(0).toUpperCase() + customer.status.slice(1)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {customer.joined}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {customer.orders}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {formatPrice(customer.totalSpent)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2">
                        <button
                          onClick={() => handleViewCustomer(customer)}
                          className="text-blue-600 hover:text-blue-900 p-1"
                          title="View Customer Details"
                        >
                          <Eye className="h-4 w-4" />
                        </button>
                        <button className="text-gray-600 hover:text-gray-900 p-1">
                          <Edit className="h-4 w-4" />
                        </button>
                        <button className="text-red-600 hover:text-red-900 p-1">
                          <Trash2 className="h-4 w-4" />
                        </button>
                        <button className="text-gray-600 hover:text-gray-900 p-1">
                          <MoreVertical className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          {filteredCustomers.length === 0 && (
            <div className="text-center py-12">
              <Users className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No customers found</h3>
              <p className="mt-1 text-sm text-gray-500">
                Try adjusting your search or filter criteria.
              </p>
            </div>
          )}
        </div>

        {/* Customer Details Modal */}
        {showCustomerModal && selectedCustomer && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
            <div className="relative bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
              <div className="flex items-center justify-between p-6 border-b border-gray-200">
                <h3 className="text-xl font-semibold text-gray-900">
                  Customer Details - {selectedCustomer.name}
                </h3>
                <button
                  onClick={() => {
                    setShowCustomerModal(false);
                    setSelectedCustomer(null);
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>

              <div className="p-6 space-y-6">
                {/* Customer Header */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="flex items-center mb-2">
                      <User className="h-5 w-5 text-gray-400 mr-2" />
                      <h4 className="font-medium text-gray-900">Personal Info</h4>
                    </div>
                    <p className="text-sm font-medium text-gray-900">{selectedCustomer.name}</p>
                    <div className="flex items-center mt-1 text-sm text-gray-600">
                      <Mail className="h-4 w-4 mr-1" />
                      {selectedCustomer.email}
                    </div>
                    <div className="flex items-center mt-1 text-sm text-gray-600">
                      <Phone className="h-4 w-4 mr-1" />
                      {selectedCustomer.phone}
                    </div>
                  </div>

                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="flex items-center mb-2">
                      <Calendar className="h-5 w-5 text-gray-400 mr-2" />
                      <h4 className="font-medium text-gray-900">Account Info</h4>
                    </div>
                    <p className="text-sm text-gray-600">Joined: {selectedCustomer.joined}</p>
                    <p className="text-sm text-gray-600">Status: 
                      <span className={`ml-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(selectedCustomer.status)}`}>
                        {selectedCustomer.status.charAt(0).toUpperCase() + selectedCustomer.status.slice(1)}
                      </span>
                    </p>
                    {selectedCustomer.lastOrderDate && (
                      <p className="text-sm text-gray-600">Last Order: {selectedCustomer.lastOrderDate}</p>
                    )}
                  </div>

                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="flex items-center mb-2">
                      <ShoppingBag className="h-5 w-5 text-gray-400 mr-2" />
                      <h4 className="font-medium text-gray-900">Order Summary</h4>
                    </div>
                    <p className="text-sm text-gray-600">Total Orders: {selectedCustomer.orders}</p>
                    <p className="text-sm font-medium text-gray-900">Total Spent: {formatPrice(selectedCustomer.totalSpent)}</p>
                    {selectedCustomer.averageOrderValue && (
                      <p className="text-sm text-gray-600">Avg Order: {formatPrice(selectedCustomer.averageOrderValue)}</p>
                    )}
                  </div>
                </div>

                {/* Address Information */}
                {selectedCustomer.address && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-4">Shipping Address</h4>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="flex items-start">
                        <MapPin className="h-5 w-5 text-gray-400 mr-2 mt-0.5" />
                        <div>
                          <p className="text-sm text-gray-900">{selectedCustomer.address.street}</p>
                          <p className="text-sm text-gray-600">
                            {selectedCustomer.address.city}, {selectedCustomer.address.state} {selectedCustomer.address.zipCode}
                          </p>
                          <p className="text-sm text-gray-600">{selectedCustomer.address.country}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Recent Orders */}
                {selectedCustomer.recentOrders && selectedCustomer.recentOrders.length > 0 && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-4">Recent Orders</h4>
                    <div className="bg-gray-50 rounded-lg overflow-hidden">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-100">
                          <tr>
                            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Order ID</th>
                            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Date</th>
                            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Amount</th>
                            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Items</th>
                            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {selectedCustomer.recentOrders.map((order) => (
                            <tr key={order.id}>
                              <td className="px-4 py-3 text-sm font-medium text-gray-900">{order.id}</td>
                              <td className="px-4 py-3 text-sm text-gray-600">{order.date}</td>
                              <td className="px-4 py-3 text-sm font-medium text-gray-900">{formatPrice(order.amount)}</td>
                              <td className="px-4 py-3 text-sm text-gray-600">{order.items}</td>
                              <td className="px-4 py-3">
                                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getOrderStatusColor(order.status)}`}>
                                  {order.status}
                                </span>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}

                {/* Customer Notes */}
                {selectedCustomer.notes && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-4">Notes</h4>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <p className="text-sm text-gray-600">{selectedCustomer.notes}</p>
                    </div>
                  </div>
                )}

                {/* Modal Actions */}
                <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                  <button
                    onClick={() => {
                      setShowCustomerModal(false);
                      setSelectedCustomer(null);
                    }}
                    className="px-6 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  );
};

export default AdminCustomersPage; 