-- Add size and color fields to products table
ALTER TABLE products 
ADD COLUMN sizes TEXT[] DEFAULT NULL,
ADD COLUMN colors TEXT[] DEFAULT NULL;

-- Add comment to explain the array fields
COMMENT ON COLUMN products.sizes IS 'Array of available sizes (e.g., ["XS", "S", "M", "L", "XL"])';
COMMENT ON COLUMN products.colors IS 'Array of available colors (e.g., ["Black", "White", "Red"])';

-- Update existing products with some default sizes and colors for clothing category
UPDATE products 
SET sizes = ARRAY['XS', 'S', 'M', 'L', 'XL', 'XXL'],
    colors = ARRAY['Black', 'White', 'Navy', 'Gray']
WHERE category_id = (SELECT id FROM categories WHERE name = 'Clothing');

-- Update electronics with some default options
UPDATE products 
SET sizes = ARRAY['One Size'],
    colors = ARRAY['Black', 'White', 'Silver']
WHERE category_id = (SELECT id FROM categories WHERE name = 'Electronics');

-- Update other categories with generic options
UPDATE products 
SET sizes = ARRAY['One Size'],
    colors = ARRAY['Standard']
WHERE sizes IS NULL OR colors IS NULL; 