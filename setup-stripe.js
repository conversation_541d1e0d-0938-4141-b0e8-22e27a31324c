#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import http from 'http';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔧 Stripe Checkout Setup Script');
console.log('================================\n');

// Check if .env files exist
const rootEnvPath = path.join(__dirname, '.env');
const serverEnvPath = path.join(__dirname, 'server', '.env');

console.log('📁 Checking environment files...');

if (!fs.existsSync(rootEnvPath)) {
  console.log('❌ Frontend .env file not found');
  console.log('📝 Please create a .env file in the root directory with:');
  console.log(`
VITE_SUPABASE_URL=your_supabase_url_here
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key_here
VITE_API_BASE_URL=http://localhost:3001/api
  `);
} else {
  console.log('✅ Frontend .env file found');
}

if (!fs.existsSync(serverEnvPath)) {
  console.log('❌ Server .env file not found');
  console.log('📝 Please create a .env file in the server directory with:');
  console.log(`
NODE_ENV=development
PORT=3001
FRONTEND_URL=http://localhost:5173
STRIPE_SECRET_KEY=sk_test_51ReoYrFRBfWA0dsqKwynq01fTWIvvrT4ifcPdLekK2kX8Wj33YKZit3HE6Tu2lQQafzuflnW88167thPRvB5TXDY00CWK49xt9
  `);
} else {
  console.log('✅ Server .env file found');
}

console.log('\n🚀 To start the application:');
console.log('1. Start the backend server: cd server && npm start');
console.log('2. Start the frontend: npm run dev');
console.log('\n📖 For detailed setup instructions, see ENVIRONMENT_SETUP.md');

// Check if servers are running
function checkServer(url, name) {
  return new Promise((resolve) => {
    const req = http.get(url, (res) => {
      console.log(`✅ ${name} is running (${res.statusCode})`);
      resolve(true);
    });
    
    req.on('error', () => {
      console.log(`❌ ${name} is not running`);
      resolve(false);
    });
    
    req.setTimeout(2000, () => {
      console.log(`❌ ${name} is not responding`);
      resolve(false);
    });
  });
}

console.log('\n🔍 Checking server status...');
Promise.all([
  checkServer('http://localhost:3001/api/health', 'Backend API'),
  checkServer('http://localhost:5173', 'Frontend Dev Server')
]).then(([backend, frontend]) => {
  if (!backend) {
    console.log('\n💡 To start the backend server:');
    console.log('cd server && npm start');
  }
  if (!frontend) {
    console.log('\n💡 To start the frontend server:');
    console.log('npm run dev');
  }
}); 