const express = require('express');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const router = express.Router();

// Create Payment Intent
router.post('/create-payment-intent', async (req, res) => {
  try {
    const { amount, currency, customer_id, order_id, metadata } = req.body;
    
    // Validate required fields
    if (!amount || amount <= 0) {
      return res.status(400).json({ error: 'Invalid amount' });
    }

    console.log('Creating payment intent:', { amount, currency, customer_id, order_id });

    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(amount),
      currency: currency.toLowerCase() || 'usd',
      ...(customer_id && customer_id.startsWith('cus_') ? { customer: customer_id } : {}),
      metadata: {
        order_id,
        ...metadata
      },
      automatic_payment_methods: {
        enabled: true,
      },
    });

    console.log('Payment intent created:', paymentIntent.id);

    res.json({
      id: paymentIntent.id,
      amount: paymentIntent.amount,
      currency: paymentIntent.currency,
      status: paymentIntent.status,
      client_secret: paymentIntent.client_secret,
    });
  } catch (error) {
    console.error('Error creating payment intent:', error);
    res.status(400).json({ error: error.message });
  }
});

// Confirm Payment
router.post('/confirm-payment', async (req, res) => {
  try {
    const { payment_intent_id, payment_method_id } = req.body;
    
    if (!payment_intent_id || !payment_method_id) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    console.log('Confirming payment:', { payment_intent_id, payment_method_id });

    const paymentIntent = await stripe.paymentIntents.confirm(payment_intent_id, {
      payment_method: payment_method_id,
    });

    console.log('Payment confirmed:', paymentIntent.status);

    res.json({ success: true, paymentIntent });
  } catch (error) {
    console.error('Error confirming payment:', error);
    res.status(400).json({ error: error.message });
  }
});

// Create Customer
router.post('/create-customer', async (req, res) => {
  try {
    const { email, name, phone, address } = req.body;
    
    if (!email || !name) {
      return res.status(400).json({ error: 'Email and name are required' });
    }

    console.log('Creating customer:', { email, name });

    const customer = await stripe.customers.create({
      email,
      name,
      phone,
      address,
    });

    console.log('Customer created:', customer.id);

    res.json({
      id: customer.id,
      email: customer.email,
      name: customer.name,
      phone: customer.phone,
      address: customer.address,
    });
  } catch (error) {
    console.error('Error creating customer:', error);
    res.status(400).json({ error: error.message });
  }
});

// Save Payment Method
router.post('/save-payment-method', async (req, res) => {
  try {
    const { customer_id, payment_method_id, is_default } = req.body;
    
    if (!customer_id || !payment_method_id) {
      return res.status(400).json({ error: 'Customer ID and payment method ID are required' });
    }

    console.log('Saving payment method:', { customer_id, payment_method_id, is_default });

    // Attach payment method to customer
    await stripe.paymentMethods.attach(payment_method_id, {
      customer: customer_id,
    });

    // Set as default if requested
    if (is_default) {
      await stripe.customers.update(customer_id, {
        invoice_settings: {
          default_payment_method: payment_method_id,
        },
      });
    }

    console.log('Payment method saved successfully');

    res.json({ success: true });
  } catch (error) {
    console.error('Error saving payment method:', error);
    res.status(400).json({ error: error.message });
  }
});

// Get Payment Methods
router.get('/payment-methods/:customerId', async (req, res) => {
  try {
    const { customerId } = req.params;
    
    if (!customerId) {
      return res.status(400).json({ error: 'Customer ID is required' });
    }

    console.log('Getting payment methods for customer:', customerId);

    const paymentMethods = await stripe.paymentMethods.list({
      customer: customerId,
      type: 'card',
    });

    // Optionally log or process paymentMethods here
    // console.log('Payment methods:', paymentMethods);

    res.json(paymentMethods);
  } catch (error) {
    console.error('Error getting payment methods:', error);
    res.status(400).json({ error: error.message });
  }
});

module.exports = router;