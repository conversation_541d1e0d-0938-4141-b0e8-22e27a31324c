{"name": "store-api-server", "version": "1.0.0", "description": "API server for e-commerce store with Stripe integration", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["stripe", "api", "payment", "express", "ecommerce"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "stripe": "^14.21.0", "dotenv": "^16.3.1", "helmet": "^7.1.0", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.3"}, "engines": {"node": ">=16.0.0"}}