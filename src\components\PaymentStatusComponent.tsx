import React from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  Clock, 
  Home, 
  Package, 
  Receipt, 
  RefreshCw, 
  ArrowLeft,
  Mail,
  Phone,
  CreditCard
} from 'lucide-react';

interface PaymentStatusComponentProps {
  status: 'success' | 'failed' | 'pending' | 'cancelled';
  paymentIntent?: any;
  orderId?: string;
  total?: number;
  orderItems?: any[];
  errorMessage?: string;
  onRetry?: () => void;
  onBack?: () => void;
}

const PaymentStatusComponent: React.FC<PaymentStatusComponentProps> = ({
  status,
  paymentIntent,
  orderId,
  total,
  orderItems,
  errorMessage,
  onRetry,
  onBack
}) => {
  const navigate = useNavigate();

  const getStatusConfig = () => {
    switch (status) {
      case 'success':
        return {
          icon: CheckCircle,
          iconColor: 'text-green-600',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          title: 'Payment Successful!',
          subtitle: 'Thank you for your purchase. Your order has been confirmed.',
          textColor: 'text-green-700'
        };
      case 'failed':
        return {
          icon: XCircle,
          iconColor: 'text-red-600',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          title: 'Payment Failed',
          subtitle: 'We encountered an issue processing your payment.',
          textColor: 'text-red-700'
        };
      case 'cancelled':
        return {
          icon: AlertCircle,
          iconColor: 'text-yellow-600',
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          title: 'Payment Cancelled',
          subtitle: 'Your payment was cancelled. You can try again or return to your cart.',
          textColor: 'text-yellow-700'
        };
      case 'pending':
        return {
          icon: Clock,
          iconColor: 'text-blue-600',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          title: 'Payment Processing',
          subtitle: 'Your payment is being processed. Please wait...',
          textColor: 'text-blue-700'
        };
      default:
        return {
          icon: AlertCircle,
          iconColor: 'text-gray-600',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          title: 'Payment Status Unknown',
          subtitle: 'Unable to determine payment status.',
          textColor: 'text-gray-700'
        };
    }
  };

  const statusConfig = getStatusConfig();
  const StatusIcon = statusConfig.icon;

  const formatPaymentMethod = (paymentMethod?: any) => {
    if (!paymentMethod) return 'N/A';
    
    if (paymentMethod.card) {
      return `•••• •••• •••• ${paymentMethod.card.last4}`;
    }
    return paymentMethod.type || 'N/A';
  };

  const getErrorDetails = () => {
    if (!errorMessage) return null;

    // Common error patterns and their explanations
    const errorPatterns = [
      {
        pattern: /card_declined/i,
        title: 'Card Declined',
        description: 'Your card was declined by your bank. Please try a different payment method.',
        solutions: [
          'Check with your bank to ensure the card is active',
          'Verify you have sufficient funds',
          'Try a different card or payment method'
        ]
      },
      {
        pattern: /insufficient_funds/i,
        title: 'Insufficient Funds',
        description: 'Your account doesn\'t have enough funds to complete this transaction.',
        solutions: [
          'Check your account balance',
          'Try a different payment method',
          'Contact your bank for assistance'
        ]
      },
      {
        pattern: /expired_card/i,
        title: 'Expired Card',
        description: 'The card you\'re using has expired.',
        solutions: [
          'Use a different card with a valid expiration date',
          'Update your card information',
          'Contact your bank for a replacement card'
        ]
      },
      {
        pattern: /invalid_cvc/i,
        title: 'Invalid Security Code',
        description: 'The security code (CVC) you entered is incorrect.',
        solutions: [
          'Double-check the 3-digit code on the back of your card',
          'For American Express cards, use the 4-digit code on the front',
          'Try entering the code again'
        ]
      },
      {
        pattern: /network_error/i,
        title: 'Network Error',
        description: 'We encountered a network issue while processing your payment.',
        solutions: [
          'Check your internet connection',
          'Try again in a few moments',
          'Contact support if the problem persists'
        ]
      }
    ];

    const matchedError = errorPatterns.find(error => 
      error.pattern.test(errorMessage)
    );

    return matchedError || {
      title: 'Payment Error',
      description: errorMessage,
      solutions: [
        'Please try again',
        'Check your payment information',
        'Contact support if the problem continues'
      ]
    };
  };

  const errorDetails = getErrorDetails();

  return (
    <div className="min-h-screen bg-gray-100 font-['Plus_Jakarta_Sans']">
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="max-w-2xl w-full bg-white rounded-2xl shadow-lg overflow-hidden">
          {/* Status Header */}
          <div className={`${statusConfig.bgColor} ${statusConfig.borderColor} border-b p-8 text-center`}>
            <div className={`w-16 h-16 ${statusConfig.bgColor} rounded-full flex items-center justify-center mx-auto mb-4`}>
              <StatusIcon className={`w-8 h-8 ${statusConfig.iconColor}`} />
            </div>
            <h1 className="text-3xl font-bold text-gray-800 mb-2">{statusConfig.title}</h1>
            <p className="text-gray-600">{statusConfig.subtitle}</p>
          </div>

          <div className="p-8">
            {/* Payment Information - Show for success and failed payments */}
            {(status === 'success' || status === 'failed') && paymentIntent && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
                <div className="flex items-center mb-4">
                  <Receipt className="w-5 h-5 text-blue-600 mr-2" />
                  <h2 className="text-lg font-semibold text-gray-800">Payment Information</h2>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  {orderId && (
                    <div>
                      <span className="text-gray-600">Order ID:</span>
                      <p className="font-medium text-gray-800 font-mono">{orderId}</p>
                    </div>
                  )}
                  <div>
                    <span className="text-gray-600">Payment ID:</span>
                    <p className="font-medium text-gray-800 font-mono">{paymentIntent.id}</p>
                  </div>
                  {total && (
                    <div>
                      <span className="text-gray-600">Amount:</span>
                      <p className="font-medium text-gray-800">${total.toFixed(2)}</p>
                    </div>
                  )}
                  <div>
                    <span className="text-gray-600">Status:</span>
                    <p className={`font-medium capitalize ${
                      status === 'success' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {paymentIntent.status}
                    </p>
                  </div>
                  {paymentIntent.payment_method && (
                    <div>
                      <span className="text-gray-600">Payment Method:</span>
                      <p className="font-medium text-gray-800">
                        {formatPaymentMethod(paymentIntent.payment_method)}
                      </p>
                    </div>
                  )}
                  {paymentIntent.created && (
                    <div>
                      <span className="text-gray-600">Date:</span>
                      <p className="font-medium text-gray-800">
                        {new Date(paymentIntent.created * 1000).toLocaleString()}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Error Details - Show for failed payments */}
            {status === 'failed' && errorDetails && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-6">
                <div className="flex items-center mb-4">
                  <XCircle className="w-5 h-5 text-red-600 mr-2" />
                  <h2 className="text-lg font-semibold text-gray-800">{errorDetails.title}</h2>
                </div>
                <p className="text-red-700 mb-4">{errorDetails.description}</p>
                <div>
                  <h3 className="font-medium text-gray-800 mb-2">What you can do:</h3>
                  <ul className="space-y-1">
                    {errorDetails.solutions.map((solution, index) => (
                      <li key={index} className="flex items-start text-sm text-gray-700">
                        <span className="w-1.5 h-1.5 bg-red-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                        {solution}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            )}

            {/* Order Summary - Show for success payments */}
            {status === 'success' && orderItems && orderItems.length > 0 && (
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-6">
                <div className="flex items-center mb-4">
                  <Package className="w-5 h-5 text-gray-600 mr-2" />
                  <h2 className="text-lg font-semibold text-gray-800">Order Summary</h2>
                </div>
                <div className="space-y-3">
                  {orderItems.map((item: any, index: number) => (
                    <div key={index} className="flex justify-between items-center py-2 border-b border-gray-200 last:border-b-0">
                      <div>
                        <p className="font-medium text-gray-800">{item.name}</p>
                        <p className="text-sm text-gray-500">Qty: {item.quantity}</p>
                      </div>
                      <p className="font-medium text-gray-800">
                        ${(item.price * item.quantity).toFixed(2)}
                      </p>
                    </div>
                  ))}
                </div>
                {total && (
                  <div className="border-t border-gray-200 pt-4 mt-4">
                    <div className="flex justify-between text-lg font-bold text-gray-800">
                      <span>Total</span>
                      <span>${total.toFixed(2)}</span>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Next Steps - Show for success payments */}
            {status === 'success' && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-3">What's Next?</h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <Mail className="w-4 h-4 text-green-600 mr-3 mt-0.5" />
                    You'll receive an order confirmation email shortly
                  </li>
                  <li className="flex items-start">
                    <Package className="w-4 h-4 text-green-600 mr-3 mt-0.5" />
                    We'll notify you when your order ships
                  </li>
                  <li className="flex items-start">
                    <Receipt className="w-4 h-4 text-green-600 mr-3 mt-0.5" />
                    Track your order status in your account dashboard
                  </li>
                </ul>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4">
              {status === 'success' ? (
                <>
                  <button
                    onClick={() => navigate('/')}
                    className="flex-1 bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center justify-center"
                  >
                    <Home className="w-4 h-4 mr-2" />
                    Continue Shopping
                  </button>
                  <button
                    onClick={() => navigate('/orders')}
                    className="flex-1 border border-gray-300 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-50 transition-colors flex items-center justify-center"
                  >
                    <Package className="w-4 h-4 mr-2" />
                    View Orders
                  </button>
                </>
              ) : status === 'failed' ? (
                <>
                  {onRetry && (
                    <button
                      onClick={onRetry}
                      className="flex-1 bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center justify-center"
                    >
                      <RefreshCw className="w-4 h-4 mr-2" />
                      Try Again
                    </button>
                  )}
                  <button
                    onClick={() => navigate('/cart')}
                    className="flex-1 border border-gray-300 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-50 transition-colors flex items-center justify-center"
                  >
                    <Package className="w-4 h-4 mr-2" />
                    Return to Cart
                  </button>
                </>
              ) : status === 'cancelled' ? (
                <>
                  {onRetry && (
                    <button
                      onClick={onRetry}
                      className="flex-1 bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center justify-center"
                    >
                      <RefreshCw className="w-4 h-4 mr-2" />
                      Try Again
                    </button>
                  )}
                  <button
                    onClick={() => navigate('/cart')}
                    className="flex-1 border border-gray-300 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-50 transition-colors flex items-center justify-center"
                  >
                    <Package className="w-4 h-4 mr-2" />
                    Return to Cart
                  </button>
                </>
              ) : (
                <button
                  onClick={() => navigate('/')}
                  className="flex-1 bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center justify-center"
                >
                  <Home className="w-4 h-4 mr-2" />
                  Go Home
                </button>
              )}

              {/* Back button for all states */}
              {onBack && (
                <button
                  onClick={onBack}
                  className="flex-1 border border-gray-300 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-50 transition-colors flex items-center justify-center"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Go Back
                </button>
              )}
            </div>

            {/* Support Information */}
            {status === 'failed' && (
              <div className="mt-6 p-4 bg-gray-50 rounded-lg text-center">
                <p className="text-sm text-gray-600 mb-2">Need help?</p>
                <div className="flex justify-center space-x-4 text-sm">
                  <a href="mailto:<EMAIL>" className="text-blue-600 hover:text-blue-700 flex items-center">
                    <Mail className="w-4 h-4 mr-1" />
                    Email Support
                  </a>
                  <a href="tel:+1234567890" className="text-blue-600 hover:text-blue-700 flex items-center">
                    <Phone className="w-4 h-4 mr-1" />
                    Call Support
                  </a>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentStatusComponent;
