# Supabase Storage Setup Guide

## Manual Setup (Recommended)

Since the client-side code cannot create storage buckets due to security restrictions, you need to create the bucket manually in your Supabase dashboard:

### 1. Go to Supabase Dashboard
- Navigate to your Supabase project dashboard
- Go to **Storage** in the left sidebar

### 2. Create Bucket
- Click **"Create a new bucket"**
- Set bucket name: `pictures`
- Make it **Public** (check the public checkbox)
- Click **"Create bucket"**

### 3. Configure Bucket Policies
After creating the bucket, you need to set up Row Level Security (RLS) policies:

#### For Public Read Access:
```sql
-- Allow public read access to all files
CREATE POLICY "Public Access" ON storage.objects FOR SELECT USING (bucket_id = 'pictures');
```

#### For Authenticated Upload:
```sql
-- Allow authenticated users to upload files
CREATE POLICY "Authenticated users can upload" ON storage.objects FOR INSERT WITH CHECK (bucket_id = 'pictures' AND auth.role() = 'authenticated');
```

#### For Authenticated Update/Delete:
```sql
-- Allow authenticated users to update/delete their files
CREATE POLICY "Users can update own files" ON storage.objects FOR UPDATE USING (bucket_id = 'pictures' AND auth.role() = 'authenticated');
CREATE POLICY "Users can delete own files" ON storage.objects FOR DELETE USING (bucket_id = 'pictures' AND auth.role() = 'authenticated');
```

### 4. Alternative: Disable RLS (For Development)
If you want to disable RLS for development (not recommended for production):

```sql
ALTER TABLE storage.objects DISABLE ROW LEVEL SECURITY;
```

## Environment Variables
Make sure your `.env` file has the correct Supabase credentials:

```env
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

## Testing
After setting up the bucket, the file upload should work. If you still get errors, check the browser console for more detailed error messages.

## Common Issues

1. **"Bucket not found"**: Make sure the bucket name is exactly `pictures`
2. **"Permission denied"**: Check that RLS policies are set up correctly
3. **"CORS error"**: Make sure your Supabase project allows requests from your domain 