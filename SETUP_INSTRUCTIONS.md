# Setup Instructions

This guide will help you fix the authentication and payment processing issues in your e-commerce store.

## Issue Summary

The errors you're seeing are caused by:
1. Missing environment variables for Stripe and Supabase
2. No authentication system configured
3. Stripe publishable key not set

## Step 1: Create Environment File

Create a `.env` file in your project root with the following content:

```env
# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_url_here
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Stripe Configuration
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key_here
```

## Step 2: Get Your Supabase Credentials

1. Go to your [Supabase Dashboard](https://supabase.com/dashboard)
2. Select your project
3. Go to **Settings** > **API**
4. Copy the **Project URL** and **anon public** key
5. Replace `your_supabase_url_here` and `your_supabase_anon_key_here` in your `.env` file

## Step 3: Get Your Stripe Credentials

1. Go to your [Stripe Dashboard](https://dashboard.stripe.com)
2. Go to **Developers** > **API keys**
3. Copy your **Publishable key** (starts with `pk_test_` for testing)
4. Replace `pk_test_your_publishable_key_here` in your `.env` file

## Step 4: Enable Authentication in Supabase

1. In your Supabase Dashboard, go to **Authentication** > **Settings**
2. Enable **Email auth** if not already enabled
3. Configure your email templates if needed

## Step 5: Set Up Database Tables

Make sure your database has the required tables. Run the migrations in the `database/migrations/` folder:

```sql
-- Check if these tables exist:
-- orders
-- order_items
-- products
-- customers
```

## Step 6: Test the Application

1. Restart your development server:
   ```bash
   npm run dev
   ```

2. Try to access the checkout page
3. You should now see a login modal instead of authentication errors
4. Create an account or sign in to complete the checkout process

## Troubleshooting

### Still seeing "User not authenticated" error?
- Make sure your `.env` file is in the project root
- Verify your Supabase URL and key are correct
- Check that authentication is enabled in Supabase

### Stripe errors?
- Verify your Stripe publishable key is correct
- Make sure you're using test keys for development
- Check that your Stripe account is active

### Database errors?
- Ensure all required tables exist
- Check that RLS (Row Level Security) policies are configured correctly
- Verify your Supabase service role key has proper permissions

## Development vs Production

For development, use:
- Test Stripe keys (`pk_test_...`)
- Supabase development project
- Test card numbers (****************)

For production, use:
- Live Stripe keys (`pk_live_...`)
- Supabase production project
- Real payment methods

## Next Steps

Once the basic setup is working, you can:
1. Customize the authentication flow
2. Add guest checkout functionality
3. Implement order management
4. Add payment method saving
5. Set up webhooks for payment processing

## Support

If you continue to have issues:
1. Check the browser console for detailed error messages
2. Verify all environment variables are set correctly
3. Ensure your Supabase and Stripe accounts are properly configured
4. Check the network tab for failed API requests 