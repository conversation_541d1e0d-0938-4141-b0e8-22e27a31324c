{"name": "stripe-api-server", "version": "1.0.0", "description": "Example API server for Stripe integration", "main": "api-server-example.js", "scripts": {"start": "node api-server-example.js", "dev": "nodemon api-server-example.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["stripe", "api", "payment", "express"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "stripe": "^14.21.0"}, "devDependencies": {"nodemon": "^3.0.3"}, "engines": {"node": ">=16.0.0"}}