import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Tag,
  ChevronDown,
  ChevronUp,
  X,
  AlertCircle,
  CheckCircle,
  MoreVertical
} from 'lucide-react';
import AdminLayout from '../components/AdminLayout';
import { CategoryService } from '../services';
import type { Category } from '../lib/supabase';
import { supabase } from '../lib/supabase';

interface CategoryFormData {
  name: string;
  description: string;
  image_url: string;
}

interface FormErrors {
  name?: string;
  description?: string;
}

const AdminCategoriesPage: React.FC = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [filteredCategories, setFilteredCategories] = useState<Category[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [formData, setFormData] = useState<CategoryFormData>({
    name: '',
    description: '',
    image_url: ''
  });
  const [imagePreviewUrl, setImagePreviewUrl] = useState<string>('');
  const [formErrors, setFormErrors] = useState<{ name?: string; description?: string } >({});
  const navigate = useNavigate();

  // Fetch categories from Supabase
  useEffect(() => {
    const isAuthenticated = localStorage.getItem('adminAuthenticated');
    if (!isAuthenticated) {
      navigate('/admin/login');
      return;
    }
    const fetchCategories = async () => {
      setIsLoading(true);
      try {
        const data = await CategoryService.getCategories();
        setCategories(data);
        setFilteredCategories(data);
      } catch (error: any) {
        setMessage({ type: 'error', text: error.message || 'Failed to fetch categories.' });
      } finally {
        setIsLoading(false);
      }
    };
    fetchCategories();
  }, [navigate]);

  useEffect(() => {
    filterAndSortCategories();
  }, [categories, searchTerm, sortBy, sortOrder]);

  // Form validation
  const validateForm = (): boolean => {
    const errors: { name?: string; description?: string } = {};
    if (!formData.name.trim()) {
      errors.name = 'Category name is required';
    }
    if (!formData.description.trim()) {
      errors.description = 'Description is required';
    }
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      image_url: ''
    });
    setImagePreviewUrl('');
    setFormErrors({});
    setEditingCategory(null);
  };

  const filterAndSortCategories = () => {
    let filtered = categories.filter(category => {
      const matchesSearch = category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (category.description?.toLowerCase().includes(searchTerm.toLowerCase()));
      return matchesSearch;
    });
    filtered.sort((a, b) => {
      let aValue: any = a[sortBy as keyof Category];
      let bValue: any = b[sortBy as keyof Category];
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }
      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });
    setFilteredCategories(filtered);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;
    setIsSubmitting(true);
    try {
      let newCategory: Category;
      if (editingCategory) {
        newCategory = await CategoryService.updateCategory({
          id: editingCategory.id,
          name: formData.name.trim(),
          description: formData.description.trim(),
          image_url: formData.image_url.trim() || null,
        });
        setCategories(prev => prev.map(c => c.id === editingCategory.id ? newCategory : c));
        setMessage({ type: 'success', text: 'Category updated successfully!' });
      } else {
        newCategory = await CategoryService.createCategory({
          name: formData.name.trim(),
          description: formData.description.trim(),
          image_url: formData.image_url.trim() || null,
        });
        setCategories(prev => [...prev, newCategory]);
        setMessage({ type: 'success', text: 'Category created successfully!' });
      }
      setShowAddModal(false);
      resetForm();
      setTimeout(() => setMessage(null), 3000);
    } catch (error: any) {
      setMessage({ type: 'error', text: error.message || 'Failed to save category. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle edit
  const handleEdit = (category: Category) => {
    setEditingCategory(category);
    setFormData({
      name: String(category.name ?? ''),
      description: String(category.description ?? ''),
      image_url: String(category.image_url ?? ''),
    });
    setImagePreviewUrl('');
    setFormErrors({});
    setShowAddModal(true);
  };

  // Handle add new
  const handleAddNew = () => {
    resetForm();
    setShowAddModal(true);
  };

  // Handle delete
  const handleDeleteCategory = async (categoryId: string) => {
    if (window.confirm('Are you sure you want to delete this category?')) {
      try {
        await CategoryService.deleteCategory(categoryId);
        setCategories(categories.filter(c => c.id !== categoryId));
        setMessage({ type: 'success', text: 'Category deleted successfully!' });
        setTimeout(() => setMessage(null), 3000);
      } catch (error: any) {
        setMessage({ type: 'error', text: error.message || 'Failed to delete category. Please try again.' });
      }
    }
  };

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    if (formErrors[name as keyof typeof formErrors]) {
      setFormErrors(prev => ({ ...prev, [name]: undefined }));
    }
  };

  // Add file upload handler
  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Show local preview immediately
    const localUrl = URL.createObjectURL(file);
    setImagePreviewUrl(localUrl);

    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `categories/${Date.now()}.${fileExt}`;
      const filePath = fileName;

      // Upload to 'pictures' bucket with upsert
      const { error } = await supabase.storage.from('pictures').upload(filePath, file, { upsert: true });
      
      if (error) {
        setMessage({ type: 'error', text: `Upload failed: ${error.message}` });
        console.error('Upload error:', error);
        setImagePreviewUrl('');
        return;
      }

      // Get public URL for preview
      const { data } = supabase.storage.from('pictures').getPublicUrl(filePath);
      const publicUrl = data?.publicUrl;
      
      if (publicUrl) {
        setFormData(prev => ({ ...prev, image_url: filePath })); // Store the file path
        setImagePreviewUrl(publicUrl); // Update preview with the actual URL
        setMessage({ type: 'success', text: 'Image uploaded successfully!' });
        setTimeout(() => setMessage(null), 2000);
      } else {
        setMessage({ type: 'error', text: 'Failed to get public URL for uploaded image' });
        setImagePreviewUrl('');
      }
    } catch (error: any) {
      setMessage({ type: 'error', text: `Upload failed: ${error.message}` });
      setImagePreviewUrl('');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'inactive':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const SortableHeader = ({ field, children }: { field: string; children: React.ReactNode }) => (
    <button
      onClick={() => {
        if (sortBy === field) {
          setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
        } else {
          setSortBy(field);
          setSortOrder('asc');
        }
      }}
      className="flex items-center space-x-1 text-left font-medium text-gray-900 hover:text-gray-600"
    >
      <span>{children}</span>
      {sortBy === field && (
        sortOrder === 'asc' ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />
      )}
    </button>
  );

  // Helper to get the correct image URL for a category
  const getCategoryImageUrl = (image_url: string) => {
    if (!image_url) return '';
    
    // If it's already a full URL (like Unsplash URLs), return it directly
    if (image_url.startsWith('http')) {
      return image_url;
    }
    
    // If it's a file path stored in our bucket, get the public URL from Supabase
    const { data } = supabase.storage.from('pictures').getPublicUrl(image_url);
    return data?.publicUrl || '';
  };

  // Component for displaying category image with loading state
  const CategoryImage = ({ imageUrl, categoryName }: { imageUrl: string; categoryName: string }) => {
    const [imageSrc, setImageSrc] = useState<string>('');
    const [isLoading, setIsLoading] = useState(true);
    const [hasError, setHasError] = useState(false);

    useEffect(() => {
      if (!imageUrl) {
        setIsLoading(false);
        return;
      }

      const url = getCategoryImageUrl(imageUrl);
      
      if (url) {
        setImageSrc(url);
      } else {
        setIsLoading(false);
        setHasError(true);
      }
    }, [imageUrl, categoryName]);

    const handleImageLoad = () => {
      setIsLoading(false);
      setHasError(false);
    };

    const handleImageError = () => {
      setIsLoading(false);
      setHasError(true);
    };

    if (!imageUrl) {
      return <span className="text-gray-400">No image</span>;
    }

    if (hasError) {
      return (
        <div className="h-12 w-12 bg-gray-100 rounded flex items-center justify-center">
          <span className="text-xs text-gray-500">Error</span>
        </div>
      );
    }

    return (
      <div className="relative h-12 w-12">
        {isLoading && (
          <div className="absolute inset-0 bg-gray-100 rounded flex items-center justify-center">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
          </div>
        )}
        <img
          src={imageSrc}
          alt={categoryName}
          className={`h-12 w-12 object-cover rounded ${isLoading ? 'opacity-0' : 'opacity-100'}`}
          onLoad={handleImageLoad}
          onError={handleImageError}
        />
      </div>
    );
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="min-h-screen bg-gray-100 flex items-center justify-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="min-h-screen bg-gray-100">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Categories</h1>
              <p className="mt-2 text-gray-600">Manage your product categories</p>
            </div>
            <button
              onClick={handleAddNew}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Category
            </button>
          </div>
        </div>

        {/* Success/Error Messages */}
        {message && (
          <div className={`mb-6 p-4 rounded-md flex items-center ${
            message.type === 'success'
              ? 'bg-green-50 border border-green-200 text-green-700'
              : 'bg-red-50 border border-red-200 text-red-700'
          }`}>
            {message.type === 'success' ? (
              <CheckCircle className="h-5 w-5 mr-2" />
            ) : (
              <AlertCircle className="h-5 w-5 mr-2" />
            )}
            {message.text}
          </div>
        )}

        {/* Filters and Search */}
        <div className="bg-white rounded-lg shadow mb-6 p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search categories..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Results count */}
            <div className="flex items-center text-sm text-gray-600">
              Showing {filteredCategories.length} of {categories.length} categories
            </div>
          </div>
        </div>

        {/* Categories Table */}
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left">
                    <SortableHeader field="name">Name</SortableHeader>
                  </th>
                  <th className="px-6 py-3 text-left">
                    <SortableHeader field="description">Description</SortableHeader>
                  </th>
                  <th className="px-6 py-3 text-left">
                    <SortableHeader field="image_url">Image</SortableHeader>
                  </th>
                  <th className="px-6 py-3 text-left">
                    <SortableHeader field="created_at">Created</SortableHeader>
                  </th>
                  <th className="px-6 py-3 text-left">
                    <SortableHeader field="updated_at">Updated</SortableHeader>
                  </th>
                  <th className="px-6 py-3 text-right">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredCategories.map((category) => (
                  <tr key={category.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{category.name}</div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900 max-w-xs truncate">{category.description}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <CategoryImage imageUrl={category.image_url || ''} categoryName={category.name || ''} />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(category.created_at).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(category.updated_at).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2">
                        <button
                          onClick={() => handleEdit(category)}
                          className="text-blue-600 hover:text-blue-900 p-1"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteCategory(category.id)}
                          className="text-red-600 hover:text-red-900 p-1"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                        <button className="text-gray-600 hover:text-gray-900 p-1">
                          <MoreVertical className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {filteredCategories.length === 0 && (
            <div className="text-center py-12">
              <Tag className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No categories found</h3>
              <p className="mt-1 text-sm text-gray-500">
                Try adjusting your search or filter criteria.
              </p>
            </div>
          )}
        </div>

        {/* Add/Edit Category Modal */}
        {showAddModal && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
            <div className="relative bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <div className="flex items-center justify-between p-6 border-b border-gray-200">
                <h3 className="text-xl font-semibold text-gray-900">
                  {editingCategory ? 'Edit Category' : 'Add New Category'}
                </h3>
                <button
                  onClick={() => {
                    setShowAddModal(false);
                    resetForm();
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>

              <form onSubmit={handleSubmit} className="p-6 space-y-6">
                {/* Category Name */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Category Name *
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name || ''}
                    onChange={handleInputChange}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${formErrors.name ? 'border-red-300' : 'border-gray-300'}`}
                    placeholder="Enter category name"
                  />
                  {formErrors.name && (
                    <p className="mt-1 text-sm text-red-600">{formErrors.name}</p>
                  )}
                </div>

                {/* Description */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description *
                  </label>
                  <textarea
                    name="description"
                    value={formData.description || ''}
                    onChange={handleInputChange}
                    rows={4}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${formErrors.description ? 'border-red-300' : 'border-gray-300'}`}
                    placeholder="Enter category description"
                  />
                  {formErrors.description && (
                    <p className="mt-1 text-sm text-red-600">{formErrors.description}</p>
                  )}
                </div>

                {/* Image Upload */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Image
                  </label>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleFileChange}
                    className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 border-gray-300"
                  />
                  {(imagePreviewUrl || formData.image_url) && (
                    <div className="mt-2">
                      <CategoryImage 
                        imageUrl={imagePreviewUrl || formData.image_url} 
                        categoryName={formData.name || 'Preview'} 
                      />
                    </div>
                  )}
                </div>

                {/* Form Actions */}
                <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={() => {
                      setShowAddModal(false);
                      resetForm();
                    }}
                    disabled={isSubmitting}
                    className="px-6 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="px-6 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        {editingCategory ? 'Updating...' : 'Creating...'}
                      </>
                    ) : (
                      editingCategory ? 'Update Category' : 'Create Category'
                    )}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  );
};

export default AdminCategoriesPage;
