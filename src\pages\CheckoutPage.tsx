import React, { useState, useEffect, useMemo } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { ArrowLeft, Loader2, AlertCircle } from 'lucide-react';
import { Elements } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';
import { useCart } from '../contexts/CartContext';
import { getImageUrl } from '../lib/utils';
import { standaloneStripeService } from '../services/standaloneStripeService';
import StripeCheckoutForm from '../components/StripeCheckoutForm';
import PaymentStatusComponent from '../components/PaymentStatusComponent';
import LoginModal from '../components/LoginModal';
import { supabase } from '../lib/supabase';

interface CartItem {
  id: string;
  name: string;
  price: number;
  image: string;
  quantity: number;
  size?: string;
  color?: string;
}

interface CheckoutData {
  cartItems?: CartItem[];
  total?: number;
  singleProduct?: {
    id: string;
    name: string;
    price: number;
    image: string;
    quantity: number;
  };
}

// Initialize Stripe with error handling
const getStripePromise = () => {
  const publishableKey = import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY;
  console.log('Stripe publishable key:', publishableKey ? 'Configured' : 'Not configured');
  
  if (!publishableKey || publishableKey === 'pk_test_your_publishable_key_here') {
    console.warn('Stripe publishable key not configured. Please set VITE_STRIPE_PUBLISHABLE_KEY in your .env file.');
    return null;
  }
  return loadStripe(publishableKey);
};

const stripePromise = getStripePromise();

const CheckoutPage = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { state: cartState, clearCart } = useCart();
  const checkoutData: CheckoutData = location.state || {};

  const [clientSecret, setClientSecret] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const [orderId, setOrderId] = useState<string>('');
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  
  // New state for payment status
  const [paymentStatus, setPaymentStatus] = useState<'pending' | 'success' | 'failed' | 'cancelled'>('pending');
  const [paymentIntent, setPaymentIntent] = useState<any>(null);

  // Use cart state if no checkout data is provided
  const getOrderItems = () => {
    if (checkoutData.singleProduct) {
      return [{
        id: checkoutData.singleProduct.id,
        name: checkoutData.singleProduct.name,
        price: checkoutData.singleProduct.price,
        quantity: checkoutData.singleProduct.quantity,
        icon: 'fa-briefcase',
        iconColor: 'text-indigo-500',
        bgColor: 'bg-indigo-100',
        description: 'Single item purchase'
      }];
    } else if (checkoutData.cartItems && checkoutData.cartItems.length > 0) {
      return checkoutData.cartItems.map(item => ({
        id: item.id,
        name: item.name,
        price: item.price,
        quantity: item.quantity,
        icon: 'fa-shopping-bag',
        iconColor: 'text-blue-500',
        bgColor: 'bg-blue-100',
        description: `${item.color || ''} ${item.size ? `• ${item.size}` : ''}`.trim() || 'Standard'
      }));
    } else if (cartState.items.length > 0) {
      // Use cart context if no checkout data
      return cartState.items.map(item => ({
        id: item.id,
        name: item.name,
        price: item.price,
        quantity: item.quantity,
        icon: 'fa-shopping-bag',
        iconColor: 'text-blue-500',
        bgColor: 'bg-blue-100',
        description: `${item.color || ''} ${item.size ? `• ${item.size}` : ''}`.trim() || 'Standard'
      }));
    }
    return [];
  };

  // Memoize order items and total to prevent infinite loops
  const orderItems = useMemo(() => getOrderItems(), [checkoutData, cartState.items]);
  const subtotal = useMemo(() => orderItems.reduce((sum, item) => sum + (item.price * item.quantity), 0), [orderItems]);
  const total = useMemo(() => subtotal, [subtotal]); // Remove tax, total equals subtotal

  // Check authentication status
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        setIsAuthenticated(!!user);
      } catch (error) {
        console.error('Auth check error:', error);
        setIsAuthenticated(false);
      }
    };
    
    checkAuth();
  }, []);

  // Create order and payment intent
  useEffect(() => {
    // Prevent multiple initializations
    if (isInitialized || orderItems.length === 0) {
      return;
    }

    const initializePayment = async () => {
      // Check if Stripe is configured
      if (!stripePromise) {
        setError('Payment processing is not configured. Please set VITE_STRIPE_PUBLISHABLE_KEY in your .env file.');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError('');
        setIsInitialized(true); // Mark as initialized to prevent re-runs

        console.log('Initializing payment with order items:', orderItems);
        console.log('Total amount:', total);

        // Get current user or create guest session
        let { data: { user } } = await supabase.auth.getUser();
        
        if (!user) {
          // For guest checkout, proceed without authentication
          console.log('No authenticated user found. Proceeding with guest checkout.');
          user = null;
        }

        // Create order in database
        const orderData = {
          customer_id: user?.id || null,
          total_amount: total,
          status: 'pending',
          payment_status: 'pending',
          shipping_address: 'To be provided',
          billing_address: 'To be provided',
          is_guest_order: !user, // Flag to identify guest orders
        };

        console.log('Creating order with data:', orderData);

        const { data: order, error: orderError } = await supabase
          .from('orders')
          .insert(orderData)
          .select()
          .single();

        if (orderError) {
          console.error('Order creation error:', orderError);
          throw new Error(`Failed to create order: ${orderError.message}`);
        }

        console.log('Order created successfully:', order.id);
        setOrderId(order.id);

        // Create order items
        const orderItemsData = orderItems.map(item => ({
          order_id: order.id,
          product_id: item.id,
          quantity: item.quantity,
          price: item.price,
        }));

        console.log('Creating order items:', orderItemsData);

        const { error: itemsError } = await supabase
          .from('order_items')
          .insert(orderItemsData);

        if (itemsError) {
          console.error('Order items creation error:', itemsError);
          throw new Error(`Failed to create order items: ${itemsError.message}`);
        }

        console.log('Order items created successfully');

        // Create payment intent
        console.log('Creating payment intent with amount:', Math.round(total * 100));
        const paymentIntent = await standaloneStripeService.createPaymentIntent({
          amount: Math.round(total * 100), // Convert to cents
          customer_id: user?.id || `guest-${order.id}`, // Use order ID for guest customers
          order_id: order.id,
          currency: 'usd',
        });

        console.log('Payment intent created successfully:', paymentIntent.id);
        setClientSecret(paymentIntent.client_secret);
      } catch (err) {
        console.error('Payment initialization error:', err);
        const errorMessage = err instanceof Error ? err.message : 'Failed to initialize payment';
        setError(errorMessage);
        setIsInitialized(false); // Reset flag on error to allow retry
        
        // Provide specific guidance based on error type
        if (errorMessage.includes('Failed to fetch') || errorMessage.includes('NetworkError')) {
          setError('Unable to connect to payment server. Please check if the backend server is running on port 3001.');
        } else if (errorMessage.includes('Stripe')) {
          setError('Stripe configuration error. Please check your Stripe keys in the .env file.');
        }
      } finally {
        setIsLoading(false);
      }
    };

    initializePayment();
  }, [orderItems.length, total, isInitialized]); // Only depend on length and total, not the objects themselves

  const handlePaymentSuccess = async (paymentIntent: any) => {
    try {
      // Clear cart after successful payment
      clearCart();
      
      // Set payment status and intent
      setPaymentStatus('success');
      setPaymentIntent(paymentIntent);
    } catch (error) {
      console.error('Error handling payment success:', error);
      setPaymentStatus('failed');
      setError('Failed to process successful payment');
    }
  };

  const handlePaymentError = (error: string) => {
    setPaymentStatus('failed');
    setError(error);
  };

  const handlePaymentCancelled = () => {
    setPaymentStatus('cancelled');
  };

  const handleRetry = () => {
    setError('');
    setPaymentStatus('pending');
    setPaymentIntent(null);
    setIsInitialized(false);
    setIsLoading(true);
  };

  const handleBack = () => {
    navigate(-1);
  };

  const handleLoginSuccess = () => {
    setIsAuthenticated(true);
    setShowLoginModal(false);
    // Reset initialization flag to allow retry
    setIsInitialized(false);
  };

  // Redirect if no items to checkout
  if (orderItems.length === 0) {
    navigate('/cart');
    return null;
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-100 font-['Plus_Jakarta_Sans'] flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Initializing payment...</p>
        </div>
      </div>
    );
  }

  // Show payment status component if payment is complete
  if (paymentStatus === 'success' || paymentStatus === 'failed' || paymentStatus === 'cancelled') {
    return (
      <PaymentStatusComponent
        status={paymentStatus}
        paymentIntent={paymentIntent}
        orderId={orderId}
        total={total}
        orderItems={orderItems}
        errorMessage={error}
        onRetry={handleRetry}
        onBack={handleBack}
      />
    );
  }

  // Show initialization errors (like Stripe not configured)
  if (error && paymentStatus === 'pending') {
    return (
      <div className="min-h-screen bg-gray-100 font-['Plus_Jakarta_Sans'] flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
            <AlertCircle className="w-6 h-6 text-red-500 mx-auto mb-2" />
            <p className="text-red-700 font-medium">Payment Error</p>
            <p className="text-red-600 text-sm mt-1">{error}</p>
          </div>
          <div className="space-y-2">
            {error.includes('log in') ? (
              <button
                onClick={() => setShowLoginModal(true)}
                className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors w-full"
              >
                Sign In to Continue
              </button>
            ) : (
              <button
                onClick={() => navigate('/cart')}
                className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors w-full"
              >
                Return to Cart
              </button>
            )}
            <button
              onClick={handleRetry}
              className="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition-colors w-full"
            >
              Retry Payment
            </button>
            {error.includes('not configured') && (
              <div className="text-xs text-gray-500 mt-2">
                <p>To enable payments, please:</p>
                <p>1. Create a .env file in your project root</p>
                <p>2. Add your Stripe publishable key</p>
                <p>3. Configure Supabase authentication</p>
              </div>
            )}
          </div>
        </div>
        
        {/* Login Modal */}
        <LoginModal
          isOpen={showLoginModal}
          onClose={() => setShowLoginModal(false)}
          onSuccess={handleLoginSuccess}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100 font-['Plus_Jakarta_Sans']">
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="max-w-4xl w-full bg-white rounded-2xl shadow-lg overflow-hidden">
          {/* Header with back button */}
          <div className="p-6 border-b border-gray-200">
            <button
              onClick={() => navigate(-1)}
              className="flex items-center text-gray-600 hover:text-black transition-colors"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to {checkoutData.singleProduct ? 'Product' : 'Cart'}
            </button>
          </div>

          <div className="md:flex">
            {/* Left Column: Order Summary */}
            <div className="md:w-2/5 p-8 bg-gray-50">
              <div className="mb-8">
                <h2 className="text-2xl font-bold text-gray-800">Order Summary</h2>
                <p className="text-gray-500 mt-2">Review your items before payment</p>
              </div>

              {/* Order Items */}
              <div className="space-y-4 mb-6">
                {orderItems.map((item, index) => (
                  <div key={index} className="flex items-center space-x-4 p-4 bg-white rounded-lg">
                    <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                      <i className={`fas ${item.icon} ${item.iconColor}`}></i>
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-800">{item.name}</h3>
                      <p className="text-sm text-gray-500">{item.description}</p>
                      <p className="text-sm text-gray-600">Qty: {item.quantity}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium text-gray-800">
                        ${(item.price * item.quantity).toFixed(2)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>

              {/* Order Totals */}
              <div className="border-t border-gray-200 pt-4 space-y-2">
                <div className="flex justify-between text-gray-600">
                  <span>Subtotal</span>
                  <span>${subtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-lg font-bold text-gray-800 border-t border-gray-200 pt-2">
                  <span>Total</span>
                  <span>${total.toFixed(2)}</span>
                </div>
              </div>
            </div>

            {/* Right Column: Payment Form */}
            <div className="md:w-3/5 p-8">
              <div className="mb-8">
                <h2 className="text-2xl font-bold text-gray-800">Complete your payment</h2>
                <p className="text-gray-500 mt-2">Enter your payment details to process your order</p>
              </div>

              {clientSecret && (
                <Elements stripe={stripePromise} options={{ clientSecret }}>
                  <StripeCheckoutForm
                    clientSecret={clientSecret}
                    amount={Math.round(total * 100)}
                    onSuccess={handlePaymentSuccess}
                    onError={handlePaymentError}
                    onCancel={handlePaymentCancelled}
                  />
                </Elements>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CheckoutPage; 