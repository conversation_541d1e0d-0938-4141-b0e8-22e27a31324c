# Standalone Stripe Integration Setup Guide

This guide will help you set up Stripe payment processing for your e-commerce store without Supabase integration.

## Prerequisites

1. A Stripe account (sign up at [stripe.com](https://stripe.com))
2. Your own API server/backend to handle Stripe operations
3. Your e-commerce store running locally

## Step 1: Stripe Account Setup

### 1.1 Get Your Stripe Keys

1. Log in to your [Stripe Dashboard](https://dashboard.stripe.com)
2. Go to **Developers** > **API keys**
3. Copy your **Publishable key** and **Secret key**
4. For testing, use the test keys (they start with `pk_test_` and `sk_test_`)

### 1.2 Set Up Webhook Endpoint

1. In your Stripe Dashboard, go to **Developers** > **Webhooks**
2. Click **Add endpoint**
3. Set the endpoint URL to: `https://your-api-domain.com/api/stripe/webhook`
4. Select these events to listen for:
   - `payment_intent.succeeded`
   - `payment_intent.payment_failed`
   - `payment_method.attached`
   - `payment_method.detached`
5. Copy the **Webhook signing secret** (starts with `whsec_`)

## Step 2: Environment Variables

Create a `.env` file in your project root with the following variables:

```env
# Supabase Configuration (for database only)
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Stripe Configuration
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key

# Your API Configuration
VITE_API_BASE_URL=https://your-api-domain.com/api
```

## Step 3: API Server Setup

You need to set up your own API server to handle Stripe operations. Here are the required endpoints:

### 3.1 Create Payment Intent Endpoint

```javascript
// POST /api/stripe/create-payment-intent
app.post('/api/stripe/create-payment-intent', async (req, res) => {
  try {
    const { amount, currency, customer_id, order_id, metadata } = req.body;
    
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(amount),
      currency: currency.toLowerCase(),
      customer: customer_id,
      metadata: {
        order_id,
        ...metadata
      },
      automatic_payment_methods: {
        enabled: true,
      },
    });

    res.json({
      id: paymentIntent.id,
      amount: paymentIntent.amount,
      currency: paymentIntent.currency,
      status: paymentIntent.status,
      client_secret: paymentIntent.client_secret,
    });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});
```

### 3.2 Confirm Payment Endpoint

```javascript
// POST /api/stripe/confirm-payment
app.post('/api/stripe/confirm-payment', async (req, res) => {
  try {
    const { payment_intent_id, payment_method_id } = req.body;
    
    const paymentIntent = await stripe.paymentIntents.confirm(payment_intent_id, {
      payment_method: payment_method_id,
    });

    res.json({ success: true, paymentIntent });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});
```

### 3.3 Create Customer Endpoint

```javascript
// POST /api/stripe/create-customer
app.post('/api/stripe/create-customer', async (req, res) => {
  try {
    const { email, name, phone, address } = req.body;
    
    const customer = await stripe.customers.create({
      email,
      name,
      phone,
      address,
    });

    res.json({
      id: customer.id,
      email: customer.email,
      name: customer.name,
      phone: customer.phone,
      address: customer.address,
    });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});
```

### 3.4 Save Payment Method Endpoint

```javascript
// POST /api/stripe/save-payment-method
app.post('/api/stripe/save-payment-method', async (req, res) => {
  try {
    const { customer_id, payment_method_id, is_default } = req.body;
    
    // Attach payment method to customer
    await stripe.paymentMethods.attach(payment_method_id, {
      customer: customer_id,
    });

    // Set as default if requested
    if (is_default) {
      await stripe.customers.update(customer_id, {
        invoice_settings: {
          default_payment_method: payment_method_id,
        },
      });
    }

    res.json({ success: true });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});
```

### 3.5 Get Payment Methods Endpoint

```javascript
// GET /api/stripe/payment-methods/:customerId
app.get('/api/stripe/payment-methods/:customerId', async (req, res) => {
  try {
    const { customerId } = req.params;
    
    const paymentMethods = await stripe.paymentMethods.list({
      customer: customerId,
      type: 'card',
    });

    res.json(paymentMethods.data);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});
```

### 3.6 Delete Payment Method Endpoint

```javascript
// DELETE /api/stripe/payment-methods/:paymentMethodId
app.delete('/api/stripe/payment-methods/:paymentMethodId', async (req, res) => {
  try {
    const { paymentMethodId } = req.params;
    
    await stripe.paymentMethods.detach(paymentMethodId);

    res.json({ success: true });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});
```

### 3.7 Get Payment History Endpoint

```javascript
// GET /api/stripe/payment-history/:customerId
app.get('/api/stripe/payment-history/:customerId', async (req, res) => {
  try {
    const { customerId } = req.params;
    
    const paymentIntents = await stripe.paymentIntents.list({
      customer: customerId,
      limit: 100,
    });

    res.json(paymentIntents.data);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});
```

### 3.8 Webhook Endpoint

```javascript
// POST /api/stripe/webhook
app.post('/api/stripe/webhook', express.raw({ type: 'application/json' }), async (req, res) => {
  const sig = req.headers['stripe-signature'];
  const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

  let event;

  try {
    event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
  } catch (err) {
    res.status(400).send(`Webhook Error: ${err.message}`);
    return;
  }

  // Handle the event
  switch (event.type) {
    case 'payment_intent.succeeded':
      const paymentIntent = event.data.object;
      // Handle successful payment
      console.log('Payment succeeded:', paymentIntent.id);
      break;
    case 'payment_intent.payment_failed':
      const failedPayment = event.data.object;
      // Handle failed payment
      console.log('Payment failed:', failedPayment.id);
      break;
    default:
      console.log(`Unhandled event type ${event.type}`);
  }

  res.json({ received: true });
});
```

## Step 4: Database Setup

Run the database migration to remove Stripe-related tables:

```sql
-- Run the migration file: database/migrations/015_remove_stripe_integration.sql
```

## Step 5: Testing

### 5.1 Test Card Numbers

Use these test card numbers for testing:

- **Success**: `4242 4242 4242 4242`
- **Decline**: `4000 0000 0000 0002`
- **Insufficient funds**: `4000 0000 0000 9995`

### 5.2 Test CVC and Expiry

- **CVC**: Any 3 digits (e.g., `123`)
- **Expiry**: Any future date (e.g., `12/25`)

## Step 6: Production Deployment

### 6.1 Environment Variables

Update your production environment variables:

```env
# Production Stripe keys
VITE_STRIPE_PUBLISHABLE_KEY=pk_live_your_live_publishable_key
STRIPE_SECRET_KEY=sk_live_your_live_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_live_webhook_secret
```

### 6.2 SSL Certificate

Ensure your API server has a valid SSL certificate for webhook delivery.

### 6.3 Error Handling

Implement proper error handling and logging in your API server.

## Troubleshooting

### Common Issues

1. **CORS errors**
   - Ensure your API server allows requests from your frontend domain
   - Add proper CORS headers

2. **Webhook not receiving events**
   - Check webhook endpoint URL
   - Verify webhook secret
   - Check API server logs

3. **Payment fails with "insufficient funds"**
   - Use test card numbers for testing
   - Check Stripe Dashboard for error details

4. **API endpoint not found**
   - Verify `VITE_API_BASE_URL` is set correctly
   - Check that all required endpoints are implemented

### Debugging

1. **Check API Server Logs**
   - Monitor your API server logs for errors
   - Check Stripe Dashboard for payment attempts

2. **Check Stripe Dashboard**
   - Go to **Payments** to see payment attempts
   - Check **Webhooks** for delivery status
   - Review **Logs** for detailed error information

## Security Considerations

1. **Never expose secret keys in client-side code**
2. **Use HTTPS in production**
3. **Validate webhook signatures**
4. **Implement proper authentication for your API**
5. **Use environment variables for sensitive data**

## Support

For additional help:

- [Stripe Documentation](https://stripe.com/docs)
- [Stripe Support](https://support.stripe.com)
- [Your API framework documentation] 