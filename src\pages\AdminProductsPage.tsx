import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  Eye,
  Package,
  DollarSign,
  Tag,
  Star,
  MoreVertical,
  ChevronDown,
  ChevronUp,
  X,
  Upload,
  AlertCircle,
  CheckCircle,
  Image as ImageIcon
} from 'lucide-react';
import AdminLayout from '../components/AdminLayout';
import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { supabase, Product, Category } from '../lib/supabase';
import { ProductService } from '../services/productService';
import { FileUploadService } from '../services/fileUploadService';
import { getImageUrl } from '../lib/utils';

interface ProductWithCategory extends Product {
  categories: {
    id: string;
    name: string;
    description: string;
  };
}

interface ProductFormData {
  name: string;
  description: string;
  price: string;
  category_id: string;
  stock_quantity: string;
  image_url: string;
  is_featured: boolean;
  sizes: string[];
  colors: string[];
  gallery: string[];
}

interface FormErrors {
  name?: string;
  description?: string;
  price?: string;
  category_id?: string;
  stock_quantity?: string;
  image?: string;
}

const AdminProductsPage: React.FC = () => {
  const [products, setProducts] = useState<ProductWithCategory[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<ProductWithCategory[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [sortBy, setSortBy] = useState('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingProduct, setEditingProduct] = useState<ProductWithCategory | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [formData, setFormData] = useState<ProductFormData>({
    name: '',
    description: '',
    price: '',
    category_id: '',
    stock_quantity: '',
    image_url: '',
    is_featured: false,
    sizes: [],
    colors: [],
    gallery: []
  });
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string>('');
  const [isUploading, setIsUploading] = useState(false);
  const [selectedGalleryFiles, setSelectedGalleryFiles] = useState<File[]>([]);
  const [galleryPreviews, setGalleryPreviews] = useState<string[]>([]);
  const [isUploadingGallery, setIsUploadingGallery] = useState(false);
  const navigate = useNavigate();

  // Load categories
  const loadCategories = async () => {
    try {
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .order('name');

      if (error) throw error;
      setCategories(data || []);
    } catch (error) {
      console.error('Error loading categories:', error);
      setMessage({ type: 'error', text: 'Failed to load categories' });
    }
  };

  // Load products
  const loadProducts = async () => {
    try {
      setIsLoading(true);
      const data = await ProductService.getProducts();
      setProducts(data || []);
      setFilteredProducts(data || []);
    } catch (error) {
      console.error('Error loading products:', error);
      setMessage({ type: 'error', text: 'Failed to load products' });
    } finally {
      setIsLoading(false);
    }
  };

  // Initialize storage bucket
  const initializeStorage = async () => {
    try {
      await FileUploadService.ensureBucketExists();
      
      // Test if we can access the bucket
      const hasAccess = await FileUploadService.testBucketAccess();
      if (!hasAccess) {
        console.warn('Storage bucket access test failed - file uploads may not work');
        setMessage({ 
          type: 'error', 
          text: 'File storage is not properly configured. You can still create products with default images.' 
        });
        // Clear the message after 5 seconds
        setTimeout(() => setMessage(null), 5000);
      }
    } catch (error) {
      console.error('Error initializing storage:', error);
      // Don't show error message to user, just log it
      // File upload will fail gracefully if needed
    }
  };

  // Handle file selection
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    // Validate files
    const validation = FileUploadService.validateFiles(files);
    if (validation.some(v => !v.isValid)) {
      setFormErrors(prev => ({ ...prev, image: validation.find(v => !v.isValid)?.error }));
      return;
    }

    setSelectedFile(files[0]);
    setFormErrors(prev => ({ ...prev, image: undefined }));

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setImagePreview(e.target?.result as string);
    };
    reader.readAsDataURL(files[0]);
  };

  // Handle gallery file selection
  const handleGalleryFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length === 0) return;

    // Validate all files
    const validations = files.map(file => FileUploadService.validateFile(file));
    const invalidFiles = validations.filter(v => !v.isValid);
    
    if (invalidFiles.length > 0) {
      setFormErrors(prev => ({ ...prev, image: invalidFiles[0].error }));
      return;
    }

    setSelectedGalleryFiles(files);
    setFormErrors(prev => ({ ...prev, image: undefined }));

    // Create previews for all files
    const newPreviews: string[] = [];
    files.forEach((file, index) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        newPreviews[index] = e.target?.result as string;
        setGalleryPreviews([...newPreviews]);
      };
      reader.readAsDataURL(file);
    });
  };

  // Upload image
  const uploadImage = async (): Promise<string> => {
    if (!selectedFile) {
      return formData.image_url || 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=500';
    }

    setIsUploading(true);
    try {
      const imageUrl = await FileUploadService.uploadFile(selectedFile, 'products');
      return imageUrl;
    } catch (error) {
      console.error('Upload error:', error);
      // Provide fallback URL if upload fails
      setMessage({ 
        type: 'error', 
        text: 'Image upload failed. Using default image. You can update the image later.' 
      });
      return 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=500';
    } finally {
      setIsUploading(false);
    }
  };

  // Upload gallery images
  const uploadGalleryImages = async (): Promise<string[]> => {
    if (selectedGalleryFiles.length === 0) {
      return formData.gallery || [];
    }

    setIsUploadingGallery(true);
    try {
      const uploadPromises = selectedGalleryFiles.map(file => 
        FileUploadService.uploadFile(file, 'products/gallery')
      );
      const uploadedUrls = await Promise.all(uploadPromises);
      return [...(formData.gallery || []), ...uploadedUrls];
    } catch (error) {
      console.error('Gallery upload error:', error);
      setMessage({ 
        type: 'error', 
        text: 'Some gallery images failed to upload. Please try again.' 
      });
      return formData.gallery || [];
    } finally {
      setIsUploadingGallery(false);
    }
  };

  // Form validation
  const validateForm = (): boolean => {
    const errors: FormErrors = {};

    if (!formData.name.trim()) {
      errors.name = 'Product name is required';
    }

    if (!formData.description.trim()) {
      errors.description = 'Description is required';
    }

    if (!formData.price.trim()) {
      errors.price = 'Price is required';
    } else if (isNaN(Number(formData.price)) || Number(formData.price) <= 0) {
      errors.price = 'Price must be a valid positive number';
    }

    if (!formData.category_id.trim()) {
      errors.category_id = 'Category is required';
    }

    if (!formData.stock_quantity.trim()) {
      errors.stock_quantity = 'Stock quantity is required';
    } else if (isNaN(Number(formData.stock_quantity)) || Number(formData.stock_quantity) < 0) {
      errors.stock_quantity = 'Stock must be a valid non-negative number';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      price: '',
      category_id: '',
      stock_quantity: '',
      image_url: '',
      is_featured: false,
      sizes: [],
      colors: [],
      gallery: []
    });
    setFormErrors({});
    setEditingProduct(null);
    setSelectedFile(null);
    setImagePreview('');
    setSelectedGalleryFiles([]);
    setGalleryPreviews([]);
  };

  useEffect(() => {
    // Check authentication
    const isAuthenticated = localStorage.getItem('adminAuthenticated');
    if (!isAuthenticated) {
      navigate('/admin/login');
      return;
    }

    // Load data
    loadCategories();
    loadProducts();
    initializeStorage();
  }, [navigate]);

  useEffect(() => {
    filterAndSortProducts();
  }, [products, searchTerm, selectedCategory, selectedStatus, sortBy, sortOrder]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      console.log('Starting form submission...');
      
      // Upload image if selected
      const imageUrl = await uploadImage();
      console.log('Main image uploaded:', imageUrl);

      // Upload gallery images if selected (store URLs for later use)
      const galleryImages = await uploadGalleryImages();
      console.log('Gallery images uploaded:', galleryImages);

      const productData = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        price: Number(formData.price),
        category_id: formData.category_id,
        stock_quantity: Number(formData.stock_quantity),
        image_url: imageUrl,
        is_featured: formData.is_featured,
        sizes: formData.sizes,
        colors: formData.colors,
        gallery: galleryImages
      };

      console.log('Product data to save:', productData);

      if (editingProduct) {
        // Update existing product
        console.log('Updating product...');
        await ProductService.updateProduct({
          id: editingProduct.id,
          ...productData
        });
        setMessage({ type: 'success', text: 'Product updated successfully!' });
      } else {
        // Add new product
        console.log('Creating new product...');
        await ProductService.createProduct(productData);
        setMessage({ type: 'success', text: 'Product created successfully!' });
      }

      setShowAddModal(false);
      resetForm();
      loadProducts(); // Reload products

      // Clear message after 3 seconds
      setTimeout(() => setMessage(null), 3000);
    } catch (error) {
      console.error('Error saving product:', error);
      setMessage({ type: 'error', text: `Failed to save product: ${error instanceof Error ? error.message : 'Unknown error'}` });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle edit
  const handleEdit = (product: ProductWithCategory) => {
    setEditingProduct(product);
    setFormData({
      name: product.name,
      description: product.description,
      price: product.price.toString(),
      category_id: product.category_id,
      stock_quantity: product.stock_quantity.toString(),
      image_url: product.image_url,
      is_featured: product.is_featured,
      sizes: product.sizes || [],
      colors: product.colors || [],
      gallery: product.gallery || []
    });
    setFormErrors({});
    setSelectedFile(null);
    setImagePreview(product.image_url);
    setSelectedGalleryFiles([]);
    setGalleryPreviews([]); // Reset gallery previews - only for new files
    setShowAddModal(true);
  };

  // Handle add new
  const handleAddNew = () => {
    resetForm();
    setShowAddModal(true);
  };

  const filterAndSortProducts = () => {
    let filtered = products.filter(product => {
      const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           product.description.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCategory = selectedCategory === 'all' || product.category_id === selectedCategory;
      const matchesStatus = selectedStatus === 'all' || 
                           (selectedStatus === 'in_stock' && product.stock_quantity > 0) ||
                           (selectedStatus === 'out_of_stock' && product.stock_quantity === 0) ||
                           (selectedStatus === 'low_stock' && product.stock_quantity <= 10 && product.stock_quantity > 0);
      
      return matchesSearch && matchesCategory && matchesStatus;
    });

    // Sort products
    filtered.sort((a, b) => {
      let aValue: any = a[sortBy as keyof ProductWithCategory];
      let bValue: any = b[sortBy as keyof ProductWithCategory];
      
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }
      
      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    setFilteredProducts(filtered);
  };

  const handleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  const getStatusColor = (stockQuantity: number) => {
    if (stockQuantity === 0) {
      return 'bg-red-100 text-red-800';
    } else if (stockQuantity <= 10) {
      return 'bg-yellow-100 text-yellow-800';
    } else {
      return 'bg-green-100 text-green-800';
    }
  };

  const getStatusText = (stockQuantity: number) => {
    if (stockQuantity === 0) {
      return 'Out of Stock';
    } else if (stockQuantity <= 10) {
      return 'Low Stock';
    } else {
      return 'In Stock';
    }
  };

  const handleDeleteProduct = async (productId: string) => {
    if (window.confirm('Are you sure you want to delete this product?')) {
      try {
        await ProductService.deleteProduct(productId);
        setMessage({ type: 'success', text: 'Product deleted successfully!' });
        loadProducts(); // Reload products
        setTimeout(() => setMessage(null), 3000);
      } catch (error) {
        console.error('Error deleting product:', error);
        setMessage({ type: 'error', text: 'Failed to delete product. Please try again.' });
      }
    }
  };

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }

    // Clear error for this field when user starts typing
    if (formErrors[name as keyof FormErrors]) {
      setFormErrors(prev => ({ ...prev, [name]: undefined }));
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  const SortableHeader = ({ field, children }: { field: string; children: React.ReactNode }) => (
    <button
      onClick={() => handleSort(field)}
      className="flex items-center space-x-1 text-xs font-medium text-gray-500 uppercase tracking-wider hover:text-gray-700"
    >
      <span>{children}</span>
      {sortBy === field && (
        sortOrder === 'asc' ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />
      )}
    </button>
  );

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="min-h-screen bg-gray-100 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading products...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <>
        <div className="min-h-screen bg-gray-100">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Products</h1>
                <p className="mt-2 text-gray-600">Manage your product catalog</p>
              </div>
              <Button
                onClick={handleAddNew}
                className="inline-flex items-center px-4 py-2 text-sm font-medium"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Product
              </Button>
            </div>
          </div>

          {/* Success/Error Messages */}
          {message && (
            <div className={`mb-6 p-4 rounded-md flex items-center ${
              message.type === 'success'
                ? 'bg-green-50 border border-green-200 text-green-700'
                : 'bg-red-50 border border-red-200 text-red-700'
            }`}>
              {message.type === 'success' ? (
                <CheckCircle className="h-5 w-5 mr-2" />
              ) : (
                <AlertCircle className="h-5 w-5 mr-2" />
              )}
              {message.text}
            </div>
          )}

          {/* Filters and Search */}
          <div className="bg-white rounded-lg shadow mb-6 p-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Search products..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 w-full"
                />
              </div>

              {/* Category Filter */}
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">All Categories</option>
                {categories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>

              {/* Status Filter */}
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">All Status</option>
                <option value="in_stock">In Stock</option>
                <option value="low_stock">Low Stock</option>
                <option value="out_of_stock">Out of Stock</option>
              </select>

              {/* Results Count */}
              <div className="flex items-center justify-end text-sm text-gray-500">
                {filteredProducts.length} of {products.length} products
              </div>
            </div>
          </div>

          {/* Products Table */}
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <div className="overflow-x-auto">
              <Table className="min-w-full divide-y divide-gray-200">
                <TableHeader className="bg-gray-50">
                  <TableRow>
                    <TableHead className="px-6 py-3 text-left">
                      <SortableHeader field="name">Product</SortableHeader>
                    </TableHead>
                    <TableHead className="px-6 py-3 text-left">
                      <SortableHeader field="category_id">Category</SortableHeader>
                    </TableHead>
                    <TableHead className="px-6 py-3 text-left">
                      <SortableHeader field="price">Price</SortableHeader>
                    </TableHead>
                    <TableHead className="px-6 py-3 text-left">
                      <SortableHeader field="stock_quantity">Stock</SortableHeader>
                    </TableHead>
                    <TableHead className="px-6 py-3 text-left">
                      <SortableHeader field="stock_quantity">Status</SortableHeader>
                    </TableHead>
                    <TableHead className="px-6 py-3 text-left">
                      <SortableHeader field="is_featured">Featured</SortableHeader>
                    </TableHead>
                    <TableHead className="px-6 py-3 text-left">
                      <SortableHeader field="gallery">Gallery</SortableHeader>
                    </TableHead>
                    <TableHead className="px-6 py-3 text-left">Variants</TableHead>
                    <TableHead className="px-6 py-3 text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody className="bg-white divide-y divide-gray-200">
                  {filteredProducts.map((product) => (
                    <TableRow key={product.id} className="hover:bg-gray-50">
                      <TableCell className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <img 
                              src={getImageUrl(product.image_url)} 
                              alt={product.name}
                              className="h-10 w-10 rounded-lg object-cover"
                              onError={(e) => {
                                (e.target as HTMLImageElement).src = 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=500';
                              }}
                            />
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">{product.name}</div>
                            <div className="text-sm text-gray-500">ID: {product.id.slice(0, 8)}...</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {product.categories?.name || 'N/A'}
                      </TableCell>
                      <TableCell className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {formatPrice(product.price)}
                      </TableCell>
                      <TableCell className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {product.stock_quantity}
                      </TableCell>
                      <TableCell className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(product.stock_quantity)}`}>
                          {getStatusText(product.stock_quantity)}
                        </span>
                      </TableCell>
                      <TableCell className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center">
                          {product.is_featured ? (
                            <Star className="h-4 w-4 text-yellow-400 fill-current" />
                          ) : (
                            <span className="text-gray-400">—</span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center">
                          {product.gallery && product.gallery.length > 0 ? (
                            <div className="flex items-center space-x-1">
                              <ImageIcon className="h-4 w-4 text-purple-500" />
                              <span className="text-sm font-medium text-purple-700">
                                {product.gallery.length} {product.gallery.length === 1 ? 'image' : 'images'}
                              </span>
                            </div>
                          ) : (
                            <span className="text-gray-400">No gallery</span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="px-6 py-4 text-sm text-gray-900">
                        <div className="space-y-1">
                          {/* Sizes */}
                          {product.sizes && product.sizes.length > 0 && (
                            <div>
                              <span className="text-xs text-gray-500">Sizes: </span>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {product.sizes.slice(0, 3).map((size, index) => (
                                  <span key={index} className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    {size}
                                  </span>
                                ))}
                                {product.sizes.length > 3 && (
                                  <span className="text-xs text-gray-500">+{product.sizes.length - 3} more</span>
                                )}
                              </div>
                            </div>
                          )}
                          
                          {/* Colors */}
                          {product.colors && product.colors.length > 0 && (
                            <div>
                              <span className="text-xs text-gray-500">Colors: </span>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {product.colors.slice(0, 3).map((color, index) => (
                                  <span key={index} className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800">
                                    {color}
                                  </span>
                                ))}
                                {product.colors.length > 3 && (
                                  <span className="text-xs text-gray-500">+{product.colors.length - 3} more</span>
                                )}
                              </div>
                            </div>
                          )}
                          
                          {(!product.sizes || product.sizes.length === 0) && (!product.colors || product.colors.length === 0) && (
                            <span className="text-xs text-gray-400">No variants</span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-2">
                          <Button
                            variant="ghost"
                            onClick={() => handleEdit(product)}
                            className="text-blue-600 hover:text-blue-900 p-1"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            onClick={() => handleDeleteProduct(product.id)}
                            className="text-red-600 hover:text-red-900 p-1"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" className="text-gray-600 hover:text-gray-900 p-1">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {filteredProducts.length === 0 && (
              <div className="text-center py-12">
                <Package className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No products found</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Try adjusting your search or filter criteria.
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Add/Edit Product Modal */}
        {showAddModal && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
            <div className="relative bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <div className="flex items-center justify-between p-6 border-b border-gray-200">
                <h3 className="text-xl font-semibold text-gray-900">
                  {editingProduct ? 'Edit Product' : 'Add New Product'}
                </h3>
                <button
                  onClick={() => {
                    setShowAddModal(false);
                    resetForm();
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>

              <form onSubmit={handleSubmit} className="p-6 space-y-6">
                {/* Product Name */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Product Name *
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      formErrors.name ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="Enter product name"
                  />
                  {formErrors.name && (
                    <p className="mt-1 text-sm text-red-600">{formErrors.name}</p>
                  )}
                </div>

                {/* Description */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description *
                  </label>
                  <textarea
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    rows={4}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      formErrors.description ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="Enter product description"
                  />
                  {formErrors.description && (
                    <p className="mt-1 text-sm text-red-600">{formErrors.description}</p>
                  )}
                </div>

                {/* Price and Stock */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Price ($) *
                    </label>
                    <input
                      type="number"
                      name="price"
                      value={formData.price}
                      onChange={handleInputChange}
                      step="0.01"
                      min="0"
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        formErrors.price ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder="0.00"
                    />
                    {formErrors.price && (
                      <p className="mt-1 text-sm text-red-600">{formErrors.price}</p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Stock Quantity *
                    </label>
                    <input
                      type="number"
                      name="stock_quantity"
                      value={formData.stock_quantity}
                      onChange={handleInputChange}
                      min="0"
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        formErrors.stock_quantity ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder="0"
                    />
                    {formErrors.stock_quantity && (
                      <p className="mt-1 text-sm text-red-600">{formErrors.stock_quantity}</p>
                    )}
                  </div>
                </div>

                {/* Category and Featured */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Category *
                    </label>
                    <select
                      name="category_id"
                      value={formData.category_id}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        formErrors.category_id ? 'border-red-300' : 'border-gray-300'
                      }`}
                    >
                      <option value="">Select category</option>
                      {categories.map(category => (
                        <option key={category.id} value={category.id}>
                          {category.name}
                        </option>
                      ))}
                    </select>
                    {formErrors.category_id && (
                      <p className="mt-1 text-sm text-red-600">{formErrors.category_id}</p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Featured Product
                    </label>
                    <div className="flex items-center mt-2">
                      <input
                        type="checkbox"
                        name="is_featured"
                        checked={formData.is_featured}
                        onChange={handleInputChange}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label className="ml-2 text-sm text-gray-700">
                        Mark as featured product
                      </label>
                    </div>
                  </div>
                </div>

                {/* Sizes and Colors */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Sizes */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Available Sizes
                    </label>
                    <div className="space-y-3">
                      {/* Add new size */}
                      <div className="flex space-x-2">
                        <input
                          type="text"
                          id="newSize"
                          placeholder="Add size (e.g., S, M, L)"
                          className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          onKeyPress={(e) => {
                            if (e.key === 'Enter') {
                              e.preventDefault();
                              const input = e.target as HTMLInputElement;
                              const newSize = input.value.trim();
                              if (newSize && (!formData.sizes || !formData.sizes.includes(newSize))) {
                                setFormData(prev => ({
                                  ...prev,
                                  sizes: [...(prev.sizes || []), newSize]
                                }));
                                input.value = '';
                              }
                            }
                          }}
                        />
                        <button
                          type="button"
                          onClick={() => {
                            const input = document.getElementById('newSize') as HTMLInputElement;
                            const newSize = input.value.trim();
                            if (newSize && (!formData.sizes || !formData.sizes.includes(newSize))) {
                              setFormData(prev => ({
                                ...prev,
                                sizes: [...(prev.sizes || []), newSize]
                              }));
                              input.value = '';
                            }
                          }}
                          className="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          Add
                        </button>
                      </div>
                      
                      {/* Display existing sizes */}
                      {formData.sizes && formData.sizes.length > 0 && (
                        <div className="flex flex-wrap gap-2">
                          {formData.sizes.map((size, index) => (
                            <span
                              key={index}
                              className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
                            >
                              {size}
                              <button
                                type="button"
                                onClick={() => {
                                  setFormData(prev => ({
                                    ...prev,
                                    sizes: prev.sizes.filter((_, i) => i !== index)
                                  }));
                                }}
                                className="ml-2 text-blue-600 hover:text-blue-800"
                              >
                                ×
                              </button>
                            </span>
                          ))}
                        </div>
                      )}
                      
                      {/* Quick size presets */}
                      <div className="text-xs text-gray-500">
                        Quick add: 
                        {['XS', 'S', 'M', 'L', 'XL', 'XXL'].map(size => (
                          <button
                            key={size}
                            type="button"
                            onClick={() => {
                              if (!formData.sizes || !formData.sizes.includes(size)) {
                                setFormData(prev => ({
                                  ...prev,
                                  sizes: [...(prev.sizes || []), size]
                                }));
                              }
                            }}
                            className="ml-1 text-blue-600 hover:text-blue-800 underline"
                          >
                            {size}
                          </button>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Colors */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Available Colors
                    </label>
                    <div className="space-y-3">
                      {/* Add new color */}
                      <div className="flex space-x-2">
                        <input
                          type="text"
                          id="newColor"
                          placeholder="Add color (e.g., Black, Red)"
                          className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          onKeyPress={(e) => {
                            if (e.key === 'Enter') {
                              e.preventDefault();
                              const input = e.target as HTMLInputElement;
                              const newColor = input.value.trim();
                              if (newColor && (!formData.colors || !formData.colors.includes(newColor))) {
                                setFormData(prev => ({
                                  ...prev,
                                  colors: [...(prev.colors || []), newColor]
                                }));
                                input.value = '';
                              }
                            }
                          }}
                        />
                        <button
                          type="button"
                          onClick={() => {
                            const input = document.getElementById('newColor') as HTMLInputElement;
                            const newColor = input.value.trim();
                            if (newColor && (!formData.colors || !formData.colors.includes(newColor))) {
                              setFormData(prev => ({
                                ...prev,
                                colors: [...(prev.colors || []), newColor]
                              }));
                              input.value = '';
                            }
                          }}
                          className="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          Add
                        </button>
                      </div>
                      
                      {/* Display existing colors */}
                      {formData.colors && formData.colors.length > 0 && (
                        <div className="flex flex-wrap gap-2">
                          {formData.colors.map((color, index) => (
                            <span
                              key={index}
                              className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800"
                            >
                              {color}
                              <button
                                type="button"
                                onClick={() => {
                                  setFormData(prev => ({
                                    ...prev,
                                    colors: prev.colors.filter((_, i) => i !== index)
                                  }));
                                }}
                                className="ml-2 text-green-600 hover:text-green-800"
                              >
                                ×
                              </button>
                            </span>
                          ))}
                        </div>
                      )}
                      
                      {/* Quick color presets */}
                      <div className="text-xs text-gray-500">
                        Quick add: 
                        {['Black', 'White', 'Red', 'Blue', 'Green', 'Yellow', 'Gray', 'Navy'].map(color => (
                          <button
                            key={color}
                            type="button"
                            onClick={() => {
                              if (!formData.colors || !formData.colors.includes(color)) {
                                setFormData(prev => ({
                                  ...prev,
                                  colors: [...(prev.colors || []), color]
                                }));
                              }
                            }}
                            className="ml-1 text-green-600 hover:text-green-800 underline"
                          >
                            {color}
                          </button>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Image Upload */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Product Image
                  </label>
                  
                  {/* Image Preview */}
                  {(imagePreview || formData.image_url) && (
                    <div className="mb-4">
                      <img
                        src={imagePreview || getImageUrl(formData.image_url)}
                        alt="Product preview"
                        className="w-32 h-32 object-cover rounded-lg border border-gray-300"
                        onError={(e) => {
                          (e.target as HTMLImageElement).src = 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=500';
                        }}
                      />
                    </div>
                  )}

                  {/* File Upload */}
                  <div className="flex items-center justify-center w-full">
                    <label className="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100">
                      <div className="flex flex-col items-center justify-center pt-5 pb-6">
                        <Upload className="w-8 h-8 mb-4 text-gray-500" />
                        <p className="mb-2 text-sm text-gray-500">
                          <span className="font-semibold">Click to upload</span> or drag and drop
                        </p>
                        <p className="text-xs text-gray-500">PNG, JPG, GIF, WebP up to 5MB</p>
                      </div>
                      <input
                        type="file"
                        className="hidden"
                        accept="image/*"
                        onChange={handleFileSelect}
                      />
                    </label>
                  </div>
                  
                  {formErrors.image && (
                    <p className="mt-1 text-sm text-red-600">{formErrors.image}</p>
                  )}
                  
                  {selectedFile && (
                    <div className="mt-2 flex items-center text-sm text-gray-600">
                      <ImageIcon className="h-4 w-4 mr-1" />
                      {selectedFile.name} ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
                    </div>
                  )}
                </div>

                {/* Gallery Upload */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Product Gallery
                  </label>
                  <p className="text-sm text-gray-500 mb-3">Upload multiple images to showcase your product from different angles</p>
                  
                  {/* Gallery Previews */}
                  {(formData.gallery && formData.gallery.length > 0) || galleryPreviews.length > 0 ? (
                    <div className="mb-4">
                      <div className="grid grid-cols-4 gap-2">
                        {/* Existing gallery images */}
                        {formData.gallery && formData.gallery.map((imageUrl, index) => (
                          <div key={`existing-${index}`} className="relative">
                            <img
                              src={getImageUrl(imageUrl)}
                              alt={`Gallery ${index + 1}`}
                              className="w-full h-24 object-cover rounded-lg border border-gray-300"
                              onError={(e) => {
                                (e.target as HTMLImageElement).src = 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=500';
                              }}
                            />
                            <button
                              type="button"
                              onClick={() => {
                                setFormData(prev => ({
                                  ...prev,
                                  gallery: prev.gallery.filter((_, i) => i !== index)
                                }));
                              }}
                              className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600"
                            >
                              ×
                            </button>
                          </div>
                        ))}
                        
                        {/* New gallery previews */}
                        {galleryPreviews.map((preview, index) => (
                          <div key={`new-${index}`} className="relative">
                            <img
                              src={preview}
                              alt={`New gallery ${index + 1}`}
                              className="w-full h-24 object-cover rounded-lg border border-gray-300"
                            />
                            <button
                              type="button"
                              onClick={() => {
                                setSelectedGalleryFiles(prev => prev.filter((_, i) => i !== index));
                                setGalleryPreviews(prev => prev.filter((_, i) => i !== index));
                              }}
                              className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600"
                            >
                              ×
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <div className="mb-4 p-4 border-2 border-dashed border-gray-300 rounded-lg text-center">
                      <ImageIcon className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                      <p className="text-sm text-gray-500">No gallery images yet. Upload some images below.</p>
                    </div>
                  )}

                  {/* Gallery File Upload */}
                  <div className="flex items-center justify-center w-full">
                    <label className="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100">
                      <div className="flex flex-col items-center justify-center pt-5 pb-6">
                        <Upload className="w-8 h-8 mb-4 text-gray-500" />
                        <p className="mb-2 text-sm text-gray-500">
                          <span className="font-semibold">Click to upload</span> multiple images
                        </p>
                        <p className="text-xs text-gray-500">PNG, JPG, GIF, WebP up to 5MB each</p>
                      </div>
                      <input
                        type="file"
                        className="hidden"
                        accept="image/*"
                        multiple
                        onChange={handleGalleryFileSelect}
                      />
                    </label>
                  </div>
                  
                  {selectedGalleryFiles.length > 0 && (
                    <div className="mt-2">
                      <p className="text-sm text-gray-600 mb-2">Selected files:</p>
                      <div className="space-y-1">
                        {selectedGalleryFiles.map((file, index) => (
                          <div key={index} className="flex items-center text-sm text-gray-600">
                            <ImageIcon className="h-4 w-4 mr-1" />
                            {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* Form Actions */}
                <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={() => {
                      setShowAddModal(false);
                      resetForm();
                    }}
                    disabled={isSubmitting}
                    className="px-6 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={isSubmitting || isUploading || isUploadingGallery}
                    className="px-6 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                  >
                    {isSubmitting || isUploading || isUploadingGallery ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        {isUploadingGallery ? 'Uploading Gallery...' : isUploading ? 'Uploading...' : (editingProduct ? 'Updating...' : 'Creating...')}
                      </>
                    ) : (
                      editingProduct ? 'Update Product' : 'Create Product'
                    )}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </>
    </AdminLayout>
  );
};

export default AdminProductsPage; 