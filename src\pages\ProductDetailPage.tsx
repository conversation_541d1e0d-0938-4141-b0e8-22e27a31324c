import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Star, Heart, Share2, Truck, Shield, RotateCcw, ChevronLeft, ChevronRight, ShoppingBag, Loader } from 'lucide-react';
import { ProductService } from '../services';
import { getImageUrl } from '../lib/utils';
import { useCart } from '../contexts/CartContext';
import type { Product } from '../lib/supabase';

const ProductDetailPage = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { addToCart } = useCart();
  const [selectedImage, setSelectedImage] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [selectedSize, setSelectedSize] = useState<string>('');
  const [selectedColor, setSelectedColor] = useState<string>('');
  const [product, setProduct] = useState<Product | null>(null);
  const [relatedProducts, setRelatedProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [imageTransition, setImageTransition] = useState(false);

  useEffect(() => {
    if (id) {
      loadProduct();
    }
  }, [id]);

  const loadProduct = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Fetch the main product
      const productData = await ProductService.getProductById(id!);
      setProduct(productData);
      
      // Set default selections for size and color
      if (productData.sizes && productData.sizes.length > 0) {
        setSelectedSize(productData.sizes[0]);
      }
      if (productData.colors && productData.colors.length > 0) {
        setSelectedColor(productData.colors[0]);
      }
      
      // Fetch related products from the same category
      if (productData.category_id) {
        const related = await ProductService.getProductsByCategory(productData.category_id, 6);
        // Filter out the current product
        const filteredRelated = related.filter(p => p.id !== productData.id);
        setRelatedProducts(filteredRelated);
      }
    } catch (error) {
      console.error('Failed to load product:', error);
      setError('Failed to load product details');
    } finally {
      setIsLoading(false);
    }
  };

  const changeImage = (newIndex: number) => {
    setImageTransition(true);
    setTimeout(() => {
      setSelectedImage(newIndex);
      setImageTransition(false);
    }, 150);
  };

  const nextImage = () => {
    const newIndex = selectedImage === productImages.length - 1 ? 0 : selectedImage + 1;
    changeImage(newIndex);
  };

  const prevImage = () => {
    const newIndex = selectedImage === 0 ? productImages.length - 1 : selectedImage - 1;
    changeImage(newIndex);
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  };

  const handleAddToCart = () => {
    if (!product) return;
    
    addToCart({
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.image_url,
      stock_quantity: product.stock_quantity,
      size: selectedSize || undefined,
      color: selectedColor || undefined,
    });
  };

  const handleAddRelatedToCart = (relatedProduct: Product) => {
    addToCart({
      id: relatedProduct.id,
      name: relatedProduct.name,
      price: relatedProduct.price,
      image: relatedProduct.image_url,
      stock_quantity: relatedProduct.stock_quantity,
    });
  };

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="flex justify-center items-center py-20">
          <Loader className="h-8 w-8 animate-spin text-gray-400" />
          <span className="ml-2 text-gray-600">Loading product details...</span>
        </div>
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="text-center py-20">
          <p className="text-red-600 mb-4">{error || 'Product not found'}</p>
          <button
            onClick={() => navigate('/products')}
            className="bg-black text-white px-6 py-2 rounded-lg hover:bg-gray-800 transition-colors"
          >
            Back to Products
          </button>
        </div>
      </div>
    );
  }

  // Generate product images array (main image first, then gallery images)
  const productImages = product.image_url 
    ? [product.image_url, ...(product.gallery || [])]
    : (product.gallery && product.gallery.length > 0) 
      ? product.gallery 
      : ['https://via.placeholder.com/600x400?text=No+Image'];

  // Generate features from description (simple approach)
  const features = product.description ? [
    'High quality materials',
    'Durable construction',
    'Modern design',
    'Easy to maintain'
  ] : [];

  return (
    <div className="max-w-7xl mx-auto px-2 sm:px-4 py-8">
      {/* Breadcrumb */}
      <nav className="text-sm text-gray-500 mb-4 sm:mb-8">
        <ol className="flex items-center space-x-2">
          <li><a href="/" className="hover:text-black">Home</a></li>
          <li>/</li>
          <li><a href="/products" className="hover:text-black">Products</a></li>
          <li>/</li>
          <li><a href={`/categories/${product.category_id}`} className="hover:text-black">{product.categories?.name || 'Uncategorized'}</a></li>
          <li>/</li>
          <li className="text-black">{product.name}</li>
        </ol>
      </nav>

      <div className="block lg:grid lg:grid-cols-2 lg:gap-12">
        {/* Product Images */}
        <div>
          <div className="relative mb-4">
            <img
              src={getImageUrl(productImages[selectedImage])}
              alt={product.name}
              className={`w-full h-72 sm:h-96 object-cover rounded-lg transition-opacity duration-300 ${
                imageTransition ? 'opacity-0' : 'opacity-100'
              }`}
              onError={(e) => {
                (e.target as HTMLImageElement).src = 'https://via.placeholder.com/600x400?text=No+Image';
              }}
            />
            
            {/* Navigation Arrows */}
            {productImages.length > 1 && (
              <>
                <button 
                  onClick={prevImage}
                  className="absolute left-2 sm:left-4 top-1/2 transform -translate-y-1/2 p-2 bg-white rounded-full shadow-md hover:bg-gray-50 transition-colors"
                >
                  <ChevronLeft className="w-5 h-5" />
                </button>
                <button 
                  onClick={nextImage}
                  className="absolute right-2 sm:right-4 top-1/2 transform -translate-y-1/2 p-2 bg-white rounded-full shadow-md hover:bg-gray-50 transition-colors"
                >
                  <ChevronRight className="w-5 h-5" />
                </button>
              </>
            )}
            
            {/* Gallery Counter */}
            {productImages.length > 1 && (
              <div className="absolute bottom-2 sm:bottom-4 right-2 sm:right-4 bg-black/70 text-white px-3 py-1 rounded-full text-xs sm:text-sm">
                {selectedImage + 1} / {productImages.length}
              </div>
            )}
          </div>
          
          {/* Thumbnails hidden */}
          {productImages.length > 1 && (
            <div className="hidden lg:grid grid-cols-5 gap-2">
              {productImages.map((image, index) => (
                <button
                  key={index}
                  onClick={() => changeImage(index)}
                  className={`border-2 rounded-lg overflow-hidden transition-all duration-200 ${
                    selectedImage === index ? 'border-black scale-105' : 'border-gray-200 hover:border-gray-400'
                  }`}
                >
                  <img
                    src={getImageUrl(image)}
                    alt={`${product.name} ${index + 1}`}
                    className="w-full h-12 sm:h-16 object-cover"
                    onError={(e) => {
                      (e.target as HTMLImageElement).src = 'https://via.placeholder.com/150x100?text=No+Image';
                    }}
                  />
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Product Info */}
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-1 sm:mb-4 text-center lg:text-left">{product.name}</h1>
          
          {/* Price */}
          <div className="flex justify-center lg:justify-start items-center mb-4 sm:mb-6">
            <span className="text-2xl sm:text-3xl font-bold text-gray-900">{formatPrice(product.price)}</span>
          </div>
          
          {/* Mobile selectors row */}
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 mb-4 sm:mb-6 items-center sm:items-end justify-center lg:justify-start">
            {/* Color Selection */}
            {product.colors && product.colors.length > 0 && (
              <div className="w-full max-w-[90px] mx-auto sm:flex-1">
                <label className="block text-xs font-semibold text-gray-500 mb-1 text-center sm:text-left">COLOR</label>
                <select
                  value={selectedColor}
                  onChange={e => setSelectedColor(e.target.value)}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 text-base focus:outline-none focus:ring-2 focus:ring-black"
                >
                  {product.colors.map((color) => (
                    <option key={color} value={color}>{color}</option>
                  ))}
                </select>
              </div>
            )}
            {/* Size Selection */}
            {product.sizes && product.sizes.length > 0 && (
              <div className="w-full max-w-[90px] mx-auto sm:flex-1">
                <label className="block text-xs font-semibold text-gray-500 mb-1 text-center sm:text-left">SIZE</label>
                <select
                  value={selectedSize}
                  onChange={e => setSelectedSize(e.target.value)}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 text-base focus:outline-none focus:ring-2 focus:ring-black"
                >
                  {product.sizes.map((size) => (
                    <option key={size} value={size}>{size}</option>
                  ))}
                </select>
              </div>
            )}
            {/* Quantity */}
            <div className="w-full max-w-[90px] mx-auto sm:flex-1">
              <label className="block text-xs font-semibold text-gray-500 mb-1 text-center sm:text-left">QUANTITY</label>
              <div className="flex items-center border border-gray-300 rounded-lg w-full">
                <button
                  onClick={() => setQuantity(Math.max(1, quantity - 1))}
                  className="px-2 py-1 sm:px-3 sm:py-2 hover:bg-gray-50 flex-1 text-base sm:text-lg"
                  disabled={product.stock_quantity === 0}
                >
                  -
                </button>
                <span className="flex-1 text-center py-1 sm:py-2 text-base sm:text-lg">{quantity}</span>
                <button
                  onClick={() => setQuantity(Math.min(product.stock_quantity, quantity + 1))}
                  className="px-2 py-1 sm:px-3 sm:py-2 hover:bg-gray-50 flex-1 text-base sm:text-lg"
                  disabled={product.stock_quantity === 0}
                >
                  +
                </button>
              </div>
            </div>
          </div>
          
          {/* Add to Cart */}
          <div className="flex flex-col gap-3 mb-4 sm:mb-6">
            <button 
              className="w-full bg-black text-white py-3 px-6 rounded-full font-medium text-base sm:text-lg hover:bg-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed border border-black"
              disabled={product.stock_quantity === 0}
              onClick={handleAddToCart}
            >
              Add to Cart
            </button>
            {/* Stripe Checkout Button */}
            <button
              className="w-full bg-[#635BFF] text-white py-3 px-6 rounded-full font-medium text-base sm:text-lg hover:bg-[#5851EC] transition-colors disabled:opacity-50 disabled:cursor-not-allowed border border-[#635BFF] flex items-center justify-center"
              disabled={product.stock_quantity === 0}
              onClick={() => navigate('/checkout', {
                state: {
                  singleProduct: {
                    id: product.id,
                    name: product.name,
                    price: product.price,
                    image: product.image_url,
                    quantity: quantity
                  }
                }
              })}
            >
              <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="currentColor">
                <path d="M13.976 9.15c-2.172-.806-3.356-1.426-3.356-2.409 0-.831.683-1.305 1.901-1.305 2.227 0 4.515.858 6.09 1.631l.89-5.494C18.252.975 15.697 0 12.165 0 9.667 0 7.589.654 6.104 1.872 4.56 3.147 3.757 4.992 3.757 7.218c0 4.039 2.467 5.76 6.476 7.219 2.585.831 3.47 1.426 3.47 2.338 0 .914-.796 1.431-2.127 1.431-1.72 0-4.516-.924-6.378-2.168l-.9 5.555C6.203 22.95 8.977 24 12.165 24c2.469 0 4.565-.624 6.041-1.708 1.736-1.176 2.597-3.157 2.597-5.624 0-4.238-2.403-5.812-6.827-7.518z"/>
              </svg>
              Pay with Stripe
            </button>
            <a href="#" className="w-full text-center text-sm underline text-gray-700 hover:text-black">More payment options</a>
          </div>
          
          {/* Features */}
          {features.length > 0 && (
            <div className="mb-6">
              <h3 className="font-semibold text-gray-900 mb-2">Features:</h3>
              <ul className="space-y-1">
                {features.map((feature, index) => (
                  <li key={index} className="text-sm text-gray-600 flex items-center">
                    <span className="w-1.5 h-1.5 bg-black rounded-full mr-2"></span>
                    {feature}
                  </li>
                ))}
              </ul>
            </div>
          )}
          
          {/* Shipping Info */}
          <div className="border-t pt-6">
            <div className="flex items-center mb-2">
              <Truck className="w-5 h-5 text-gray-500 mr-2" />
              <span className="text-sm text-gray-600">Free shipping on orders over $100</span>
            </div>
            <div className="flex items-center mb-2">
              <Shield className="w-5 h-5 text-gray-500 mr-2" />
              <span className="text-sm text-gray-600">30-day return policy</span>
            </div>
            <div className="flex items-center">
              <RotateCcw className="w-5 h-5 text-gray-500 mr-2" />
              <span className="text-sm text-gray-600">Easy exchanges</span>
            </div>
          </div>

          {/* Category hidden */}
          <div className="mb-2 sm:mb-4 hidden lg:block">
            <span className="text-xs sm:text-sm text-gray-500 uppercase tracking-wide">{product.categories?.name || 'Uncategorized'}</span>
          </div>
        </div>
      </div>

      {/* Enhanced Related Products */}
      {relatedProducts.length > 0 && (
        <div className="mt-20 bg-gray-50 py-16 -mx-2 sm:-mx-4 px-2 sm:px-4">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">You might also like</h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Discover more products that complement your style and preferences
              </p>
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {relatedProducts.map((relatedProduct) => (
                <div
                  key={relatedProduct.id}
                  className="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-xl transition-all duration-300 cursor-pointer group border border-gray-100"
                  onClick={() => navigate(`/products/${relatedProduct.id}`)}
                >
                  <div className="relative">
                    <img
                      src={getImageUrl(relatedProduct.image_url)}
                      alt={relatedProduct.name}
                      className="w-full h-56 object-cover group-hover:scale-105 transition-transform duration-300"
                      onError={(e) => {
                        (e.target as HTMLImageElement).src = 'https://via.placeholder.com/300x400?text=No+Image';
                      }}
                    />
                    
                    {/* Stock Badge */}
                    {relatedProduct.stock_quantity <= 5 && relatedProduct.stock_quantity > 0 && (
                      <span className="absolute top-3 left-3 bg-orange-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                        Only {relatedProduct.stock_quantity} left
                      </span>
                    )}
                    
                    {relatedProduct.stock_quantity === 0 && (
                      <span className="absolute top-3 left-3 bg-red-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                        Out of Stock
                      </span>
                    )}
                    
                    {/* Category Badge */}
                    <span className="absolute top-3 right-3 bg-black/80 text-white text-xs px-2 py-1 rounded-full">
                      {relatedProduct.categories?.name || 'Uncategorized'}
                    </span>
                    
                    {/* Quick Actions */}
                    <div className="absolute bottom-3 right-3 flex flex-col space-y-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <button 
                        className="bg-white p-2 rounded-full shadow-lg hover:bg-gray-100 transition-colors"
                        onClick={e => {e.stopPropagation(); /* Add to wishlist */}}
                      >
                        <Heart className="w-4 h-4" />
                      </button>
                      <button 
                        className="bg-black text-white p-2 rounded-full shadow-lg hover:bg-gray-800 transition-colors disabled:opacity-50"
                        onClick={e => {e.stopPropagation(); handleAddRelatedToCart(relatedProduct);}}
                        disabled={relatedProduct.stock_quantity === 0}
                      >
                        <ShoppingBag className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                  
                  <div className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-xs text-gray-500 uppercase tracking-wide">{relatedProduct.categories?.name || 'Uncategorized'}</span>
                    </div>
                    
                    <h3 className="font-semibold text-gray-900 mb-2 group-hover:text-black transition-colors line-clamp-2">
                      {relatedProduct.name}
                    </h3>
                    
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-2">
                        <span className="font-bold text-lg text-gray-900">{formatPrice(relatedProduct.price)}</span>
                      </div>
                    </div>
                    
                    {/* Show available variants */}
                    {(relatedProduct.sizes && relatedProduct.sizes.length > 0) || (relatedProduct.colors && relatedProduct.colors.length > 0) ? (
                      <div className="mb-3 text-xs text-gray-500">
                        {relatedProduct.sizes && relatedProduct.sizes.length > 0 && (
                          <span className="mr-2">Sizes: {relatedProduct.sizes.slice(0, 2).join(', ')}{relatedProduct.sizes.length > 2 && '...'}</span>
                        )}
                        {relatedProduct.colors && relatedProduct.colors.length > 0 && (
                          <span>Colors: {relatedProduct.colors.slice(0, 2).join(', ')}{relatedProduct.colors.length > 2 && '...'}</span>
                        )}
                      </div>
                    ) : null}
                    
                    <div className="flex space-x-2">
                      <button
                        className="flex-1 bg-black text-white py-2 px-3 rounded-lg text-sm font-medium hover:bg-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                        onClick={e => {e.stopPropagation(); handleAddRelatedToCart(relatedProduct);}}
                        disabled={relatedProduct.stock_quantity === 0}
                      >
                        Add to Cart
                      </button>
                      <button
                        className="flex-1 bg-gray-100 text-black py-2 px-3 rounded-lg text-sm font-medium hover:bg-gray-200 transition-colors"
                        onClick={e => {e.stopPropagation(); navigate(`/products/${relatedProduct.id}`);}}
                      >
                        View
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="text-center mt-12">
              <button 
                className="bg-black text-white px-8 py-3 rounded-lg font-medium hover:bg-gray-800 transition-colors"
                onClick={() => navigate('/products')}
              >
                View All Products
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductDetailPage; 