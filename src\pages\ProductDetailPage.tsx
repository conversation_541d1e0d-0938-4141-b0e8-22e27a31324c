import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Star,
  Heart,
  Share2,
  Truck,
  Shield,
  RotateCcw,
  ChevronLeft,
  ChevronRight,
  ShoppingBag,
  Loader,
  ZoomIn,
  Facebook,
  Twitter,
  Instagram,
  Copy,
  Check,
  Eye,
  Clock,
  Award,
  Users,
  ThumbsUp,
  MessageCircle,
  ChevronDown,
  ChevronUp,
  Package,
  CreditCard,
  RefreshCw
} from 'lucide-react';
import { ProductService } from '../services';
import { getImageUrl } from '../lib/utils';
import { useCart } from '../contexts/CartContext';
import type { Product } from '../lib/supabase';

const ProductDetailPage = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { addToCart } = useCart();
  const [selectedImage, setSelectedImage] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [selectedSize, setSelectedSize] = useState<string>('');
  const [selectedColor, setSelectedColor] = useState<string>('');
  const [product, setProduct] = useState<Product | null>(null);
  const [relatedProducts, setRelatedProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [imageTransition, setImageTransition] = useState(false);

  // New state for enhanced functionality
  const [isWishlisted, setIsWishlisted] = useState(false);
  const [showImageZoom, setShowImageZoom] = useState(false);
  const [activeTab, setActiveTab] = useState<'description' | 'specifications' | 'reviews' | 'shipping'>('description');
  const [showAllReviews, setShowAllReviews] = useState(false);
  const [copiedLink, setCopiedLink] = useState(false);
  const [recentlyViewed, setRecentlyViewed] = useState<Product[]>([]);

  useEffect(() => {
    if (id) {
      loadProduct();
    }
  }, [id]);

  const loadProduct = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Fetch the main product
      const productData = await ProductService.getProductById(id!);
      setProduct(productData);
      
      // Set default selections for size and color
      if (productData.sizes && productData.sizes.length > 0) {
        setSelectedSize(productData.sizes[0]);
      }
      if (productData.colors && productData.colors.length > 0) {
        setSelectedColor(productData.colors[0]);
      }
      
      // Fetch related products from the same category
      if (productData.category_id) {
        const related = await ProductService.getProductsByCategory(productData.category_id, 6);
        // Filter out the current product
        const filteredRelated = related.filter(p => p.id !== productData.id);
        setRelatedProducts(filteredRelated);
      }
    } catch (error) {
      console.error('Failed to load product:', error);
      setError('Failed to load product details');
    } finally {
      setIsLoading(false);
    }
  };

  const changeImage = (newIndex: number) => {
    setImageTransition(true);
    setTimeout(() => {
      setSelectedImage(newIndex);
      setImageTransition(false);
    }, 150);
  };

  const nextImage = () => {
    const newIndex = selectedImage === productImages.length - 1 ? 0 : selectedImage + 1;
    changeImage(newIndex);
  };

  const prevImage = () => {
    const newIndex = selectedImage === 0 ? productImages.length - 1 : selectedImage - 1;
    changeImage(newIndex);
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  };

  // Mock data for enhanced features (in a real app, this would come from the database)
  const mockReviews = [
    {
      id: '1',
      user: 'Sarah M.',
      rating: 5,
      date: '2024-01-15',
      title: 'Excellent quality!',
      comment: 'This product exceeded my expectations. The quality is outstanding and it arrived quickly.',
      verified: true,
      helpful: 12
    },
    {
      id: '2',
      user: 'Mike R.',
      rating: 4,
      date: '2024-01-10',
      title: 'Good value for money',
      comment: 'Great product overall. Minor issues with packaging but the item itself is perfect.',
      verified: true,
      helpful: 8
    },
    {
      id: '3',
      user: 'Emma L.',
      rating: 5,
      date: '2024-01-05',
      title: 'Love it!',
      comment: 'Absolutely love this product. Will definitely buy again.',
      verified: false,
      helpful: 5
    }
  ];

  const averageRating = mockReviews.reduce((acc, review) => acc + review.rating, 0) / mockReviews.length;
  const totalReviews = mockReviews.length;

  // Enhanced product specifications (mock data)
  const getProductSpecifications = () => {
    if (!product) return [];

    return [
      { label: 'Brand', value: 'Premium Brand' },
      { label: 'Material', value: 'High-quality materials' },
      { label: 'Dimensions', value: '10" x 8" x 2"' },
      { label: 'Weight', value: '1.2 lbs' },
      { label: 'Color Options', value: product.colors?.join(', ') || 'Standard' },
      { label: 'Size Options', value: product.sizes?.join(', ') || 'One Size' },
      { label: 'Care Instructions', value: 'Easy care and maintenance' },
      { label: 'Warranty', value: '1 Year Limited Warranty' }
    ];
  };

  // Social sharing functions
  const handleShare = async (platform: string) => {
    const url = window.location.href;
    const text = `Check out this amazing product: ${product?.name}`;

    switch (platform) {
      case 'facebook':
        window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`, '_blank');
        break;
      case 'twitter':
        window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`, '_blank');
        break;
      case 'copy':
        try {
          await navigator.clipboard.writeText(url);
          setCopiedLink(true);
          setTimeout(() => setCopiedLink(false), 2000);
        } catch (err) {
          console.error('Failed to copy link:', err);
        }
        break;
    }
  };

  const handleWishlistToggle = () => {
    setIsWishlisted(!isWishlisted);
    // In a real app, this would save to the user's wishlist
  };

  const handleCompareProduct = () => {
    // In a real app, this would add the product to a comparison list
    alert('Product added to comparison! (Feature would be implemented with state management)');
  };

  const handleAddToCart = () => {
    if (!product) return;
    
    addToCart({
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.image_url,
      stock_quantity: product.stock_quantity,
      size: selectedSize || undefined,
      color: selectedColor || undefined,
    });
  };

  const handleAddRelatedToCart = (relatedProduct: Product) => {
    addToCart({
      id: relatedProduct.id,
      name: relatedProduct.name,
      price: relatedProduct.price,
      image: relatedProduct.image_url,
      stock_quantity: relatedProduct.stock_quantity,
    });
  };

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="flex justify-center items-center py-20">
          <Loader className="h-8 w-8 animate-spin text-gray-400" />
          <span className="ml-2 text-gray-600">Loading product details...</span>
        </div>
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="text-center py-20">
          <p className="text-red-600 mb-4">{error || 'Product not found'}</p>
          <button
            onClick={() => navigate('/products')}
            className="bg-black text-white px-6 py-2 rounded-lg hover:bg-gray-800 transition-colors"
          >
            Back to Products
          </button>
        </div>
      </div>
    );
  }

  // Generate product images array (main image first, then gallery images)
  const productImages = product.image_url 
    ? [product.image_url, ...(product.gallery || [])]
    : (product.gallery && product.gallery.length > 0) 
      ? product.gallery 
      : ['https://via.placeholder.com/600x400?text=No+Image'];

  // Generate features from description (simple approach)
  const features = product.description ? [
    'High quality materials',
    'Durable construction',
    'Modern design',
    'Easy to maintain'
  ] : [];

  return (
    <div className="max-w-7xl mx-auto px-2 sm:px-4 py-8">
      {/* Breadcrumb */}
      <nav className="text-sm text-gray-500 mb-4 sm:mb-8">
        <ol className="flex items-center space-x-2">
          <li><a href="/" className="hover:text-black">Home</a></li>
          <li>/</li>
          <li><a href="/products" className="hover:text-black">Products</a></li>
          <li>/</li>
          <li><a href={`/categories/${product.category_id}`} className="hover:text-black">{product.categories?.name || 'Uncategorized'}</a></li>
          <li>/</li>
          <li className="text-black">{product.name}</li>
        </ol>
      </nav>

      <div className="block lg:grid lg:grid-cols-2 lg:gap-12">
        {/* Enhanced Product Images */}
        <div>
          <div className="relative mb-4 group">
            <div className="relative overflow-hidden rounded-xl bg-gray-100">
              <img
                src={getImageUrl(productImages[selectedImage])}
                alt={product.name}
                className={`w-full h-72 sm:h-96 lg:h-[500px] object-cover transition-all duration-300 cursor-zoom-in ${
                  imageTransition ? 'opacity-0 scale-105' : 'opacity-100 scale-100'
                } hover:scale-110`}
                onClick={() => setShowImageZoom(true)}
                onError={(e) => {
                  (e.target as HTMLImageElement).src = 'https://via.placeholder.com/600x400?text=No+Image';
                }}
              />

              {/* Zoom Icon */}
              <div className="absolute top-4 left-4 bg-black/70 text-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                <ZoomIn className="w-5 h-5" />
              </div>

              {/* Navigation Arrows */}
              {productImages.length > 1 && (
                <>
                  <button
                    onClick={prevImage}
                    className="absolute left-2 sm:left-4 top-1/2 transform -translate-y-1/2 p-3 bg-white/90 backdrop-blur-sm rounded-full shadow-lg hover:bg-white transition-all duration-200 opacity-0 group-hover:opacity-100"
                  >
                    <ChevronLeft className="w-5 h-5" />
                  </button>
                  <button
                    onClick={nextImage}
                    className="absolute right-2 sm:right-4 top-1/2 transform -translate-y-1/2 p-3 bg-white/90 backdrop-blur-sm rounded-full shadow-lg hover:bg-white transition-all duration-200 opacity-0 group-hover:opacity-100"
                  >
                    <ChevronRight className="w-5 h-5" />
                  </button>
                </>
              )}

              {/* Gallery Counter */}
              {productImages.length > 1 && (
                <div className="absolute bottom-4 right-4 bg-black/80 backdrop-blur-sm text-white px-3 py-2 rounded-full text-sm font-medium">
                  {selectedImage + 1} / {productImages.length}
                </div>
              )}
            </div>
          </div>

          {/* Enhanced Thumbnails */}
          {productImages.length > 1 && (
            <div className="grid grid-cols-4 sm:grid-cols-5 lg:grid-cols-6 gap-2 sm:gap-3">
              {productImages.map((image, index) => (
                <button
                  key={index}
                  onClick={() => changeImage(index)}
                  className={`relative border-2 rounded-lg overflow-hidden transition-all duration-200 aspect-square ${
                    selectedImage === index
                      ? 'border-black ring-2 ring-black ring-offset-2 scale-105'
                      : 'border-gray-200 hover:border-gray-400 hover:scale-105'
                  }`}
                >
                  <img
                    src={getImageUrl(image)}
                    alt={`${product.name} ${index + 1}`}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      (e.target as HTMLImageElement).src = 'https://via.placeholder.com/150x100?text=No+Image';
                    }}
                  />
                  {selectedImage === index && (
                    <div className="absolute inset-0 bg-black/20 flex items-center justify-center">
                      <Eye className="w-4 h-4 text-white" />
                    </div>
                  )}
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Image Zoom Modal */}
        {showImageZoom && (
          <div className="fixed inset-0 z-50 bg-black/90 flex items-center justify-center p-4" onClick={() => setShowImageZoom(false)}>
            <div className="relative max-w-4xl max-h-full">
              <button
                onClick={() => setShowImageZoom(false)}
                className="absolute top-4 right-4 text-white bg-black/50 rounded-full p-2 hover:bg-black/70 transition-colors z-10"
              >
                <X className="w-6 h-6" />
              </button>
              <img
                src={getImageUrl(productImages[selectedImage])}
                alt={product.name}
                className="max-w-full max-h-full object-contain rounded-lg"
                onClick={(e) => e.stopPropagation()}
              />
              {productImages.length > 1 && (
                <>
                  <button
                    onClick={(e) => { e.stopPropagation(); prevImage(); }}
                    className="absolute left-4 top-1/2 transform -translate-y-1/2 p-3 bg-white/20 backdrop-blur-sm text-white rounded-full hover:bg-white/30 transition-colors"
                  >
                    <ChevronLeft className="w-6 h-6" />
                  </button>
                  <button
                    onClick={(e) => { e.stopPropagation(); nextImage(); }}
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 p-3 bg-white/20 backdrop-blur-sm text-white rounded-full hover:bg-white/30 transition-colors"
                  >
                    <ChevronRight className="w-6 h-6" />
                  </button>
                </>
              )}
            </div>
          </div>
        )}

        {/* Product Info */}
        <div>
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-2 sm:mb-4 text-center lg:text-left leading-tight">{product.name}</h1>

          {/* Rating and Reviews */}
          <div className="flex justify-center lg:justify-start items-center mb-4 space-x-4">
            <div className="flex items-center space-x-1">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className={`w-5 h-5 ${
                    i < Math.floor(averageRating)
                      ? 'text-yellow-400 fill-current'
                      : 'text-gray-300'
                  }`}
                />
              ))}
              <span className="text-sm text-gray-600 ml-2">
                {averageRating.toFixed(1)} ({totalReviews} reviews)
              </span>
            </div>
          </div>

          {/* Price */}
          <div className="flex justify-center lg:justify-start items-center mb-6">
            <span className="text-3xl sm:text-4xl font-bold text-gray-900">{formatPrice(product.price)}</span>
            <span className="text-lg text-gray-500 ml-2 line-through">{formatPrice(product.price * 1.2)}</span>
            <span className="bg-red-100 text-red-800 text-sm font-medium px-2 py-1 rounded-full ml-3">
              Save 17%
            </span>
          </div>

          {/* Stock Status */}
          <div className="flex justify-center lg:justify-start items-center mb-6">
            <div className={`flex items-center space-x-2 px-3 py-1 rounded-full text-sm font-medium ${
              product.stock_quantity > 10
                ? 'bg-green-100 text-green-800'
                : product.stock_quantity > 0
                  ? 'bg-yellow-100 text-yellow-800'
                  : 'bg-red-100 text-red-800'
            }`}>
              <Package className="w-4 h-4" />
              <span>
                {product.stock_quantity > 10
                  ? 'In Stock'
                  : product.stock_quantity > 0
                    ? `Only ${product.stock_quantity} left`
                    : 'Out of Stock'
                }
              </span>
            </div>
          </div>
          
          {/* Mobile selectors row */}
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 mb-4 sm:mb-6 items-center sm:items-end justify-center lg:justify-start">
            {/* Color Selection */}
            {product.colors && product.colors.length > 0 && (
              <div className="w-full max-w-[90px] mx-auto sm:flex-1">
                <label className="block text-xs font-semibold text-gray-500 mb-1 text-center sm:text-left">COLOR</label>
                <select
                  value={selectedColor}
                  onChange={e => setSelectedColor(e.target.value)}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 text-base focus:outline-none focus:ring-2 focus:ring-black"
                >
                  {product.colors.map((color) => (
                    <option key={color} value={color}>{color}</option>
                  ))}
                </select>
              </div>
            )}
            {/* Size Selection */}
            {product.sizes && product.sizes.length > 0 && (
              <div className="w-full max-w-[90px] mx-auto sm:flex-1">
                <label className="block text-xs font-semibold text-gray-500 mb-1 text-center sm:text-left">SIZE</label>
                <select
                  value={selectedSize}
                  onChange={e => setSelectedSize(e.target.value)}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 text-base focus:outline-none focus:ring-2 focus:ring-black"
                >
                  {product.sizes.map((size) => (
                    <option key={size} value={size}>{size}</option>
                  ))}
                </select>
              </div>
            )}
            {/* Enhanced Quantity Selector */}
            <div className="w-full max-w-[120px] mx-auto sm:flex-1">
              <label className="block text-xs font-semibold text-gray-500 mb-1 text-center sm:text-left">QUANTITY</label>
              <div className="flex items-center border-2 border-gray-300 rounded-lg w-full bg-white shadow-sm">
                <button
                  onClick={() => setQuantity(Math.max(1, quantity - 1))}
                  className="px-3 py-2 hover:bg-gray-50 transition-colors text-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                  disabled={product.stock_quantity === 0 || quantity <= 1}
                >
                  −
                </button>
                <div className="flex-1 text-center py-2">
                  <input
                    type="number"
                    min="1"
                    max={product.stock_quantity}
                    value={quantity}
                    onChange={(e) => {
                      const value = parseInt(e.target.value) || 1;
                      setQuantity(Math.min(Math.max(1, value), product.stock_quantity));
                    }}
                    className="w-full text-center text-lg font-medium bg-transparent border-none outline-none"
                    disabled={product.stock_quantity === 0}
                  />
                </div>
                <button
                  onClick={() => setQuantity(Math.min(product.stock_quantity, quantity + 1))}
                  className="px-3 py-2 hover:bg-gray-50 transition-colors text-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                  disabled={product.stock_quantity === 0 || quantity >= product.stock_quantity}
                >
                  +
                </button>
              </div>
              {product.stock_quantity > 0 && quantity === product.stock_quantity && (
                <p className="text-xs text-orange-600 mt-1 text-center">Max quantity reached</p>
              )}
            </div>
          </div>
          
          {/* Add to Cart */}
          <div className="space-y-4 mb-6">
            <div className="flex gap-3">
              <button
                className="flex-1 bg-black text-white py-4 px-6 rounded-lg font-semibold text-lg hover:bg-gray-800 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105 active:scale-95 shadow-lg"
                disabled={product.stock_quantity === 0}
                onClick={handleAddToCart}
              >
                <ShoppingBag className="w-5 h-5 inline mr-2" />
                Add to Cart
              </button>
              <button
                onClick={handleWishlistToggle}
                className={`p-4 rounded-lg border-2 transition-all duration-200 hover:scale-105 active:scale-95 ${
                  isWishlisted
                    ? 'bg-red-50 border-red-200 text-red-600'
                    : 'bg-gray-50 border-gray-200 text-gray-600 hover:border-gray-300'
                }`}
                title="Add to Wishlist"
              >
                <Heart className={`w-6 h-6 ${isWishlisted ? 'fill-current' : ''}`} />
              </button>
              <button
                onClick={handleCompareProduct}
                className="p-4 rounded-lg border-2 bg-gray-50 border-gray-200 text-gray-600 hover:border-gray-300 hover:bg-gray-100 transition-all duration-200 hover:scale-105 active:scale-95"
                title="Compare Product"
              >
                <Eye className="w-6 h-6" />
              </button>
            </div>

            {/* Buy Now Button */}
            <button
              className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-4 px-6 rounded-lg font-semibold text-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105 active:scale-95 shadow-lg flex items-center justify-center"
              disabled={product.stock_quantity === 0}
              onClick={() => navigate('/checkout', {
                state: {
                  singleProduct: {
                    id: product.id,
                    name: product.name,
                    price: product.price,
                    image: product.image_url,
                    quantity: quantity
                  }
                }
              })}
            >
              <CreditCard className="w-5 h-5 mr-2" />
              Buy Now
            </button>

            {/* Social Sharing */}
            <div className="flex items-center justify-center space-x-4 pt-4 border-t">
              <span className="text-sm text-gray-600">Share:</span>
              <button
                onClick={() => handleShare('facebook')}
                className="p-2 text-blue-600 hover:bg-blue-50 rounded-full transition-colors"
              >
                <Facebook className="w-5 h-5" />
              </button>
              <button
                onClick={() => handleShare('twitter')}
                className="p-2 text-blue-400 hover:bg-blue-50 rounded-full transition-colors"
              >
                <Twitter className="w-5 h-5" />
              </button>
              <button
                onClick={() => handleShare('copy')}
                className="p-2 text-gray-600 hover:bg-gray-50 rounded-full transition-colors"
              >
                {copiedLink ? <Check className="w-5 h-5 text-green-600" /> : <Copy className="w-5 h-5" />}
              </button>
            </div>
          </div>
          
          {/* Features */}
          {features.length > 0 && (
            <div className="mb-6">
              <h3 className="font-semibold text-gray-900 mb-2">Features:</h3>
              <ul className="space-y-1">
                {features.map((feature, index) => (
                  <li key={index} className="text-sm text-gray-600 flex items-center">
                    <span className="w-1.5 h-1.5 bg-black rounded-full mr-2"></span>
                    {feature}
                  </li>
                ))}
              </ul>
            </div>
          )}
          
          {/* Enhanced Trust Signals */}
          <div className="border-t pt-6 space-y-4">
            <h3 className="font-semibold text-gray-900 mb-4">Why Choose Us</h3>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="flex items-center p-3 bg-green-50 rounded-lg">
                <div className="bg-green-100 p-2 rounded-full mr-3">
                  <Truck className="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <p className="font-medium text-green-900">Free Shipping</p>
                  <p className="text-sm text-green-700">On orders over $100</p>
                </div>
              </div>

              <div className="flex items-center p-3 bg-blue-50 rounded-lg">
                <div className="bg-blue-100 p-2 rounded-full mr-3">
                  <Shield className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <p className="font-medium text-blue-900">Secure Checkout</p>
                  <p className="text-sm text-blue-700">SSL encrypted</p>
                </div>
              </div>

              <div className="flex items-center p-3 bg-purple-50 rounded-lg">
                <div className="bg-purple-100 p-2 rounded-full mr-3">
                  <RotateCcw className="w-5 h-5 text-purple-600" />
                </div>
                <div>
                  <p className="font-medium text-purple-900">Easy Returns</p>
                  <p className="text-sm text-purple-700">30-day policy</p>
                </div>
              </div>

              <div className="flex items-center p-3 bg-orange-50 rounded-lg">
                <div className="bg-orange-100 p-2 rounded-full mr-3">
                  <Award className="w-5 h-5 text-orange-600" />
                </div>
                <div>
                  <p className="font-medium text-orange-900">Quality Guarantee</p>
                  <p className="text-sm text-orange-700">Premium materials</p>
                </div>
              </div>
            </div>

            {/* Customer Satisfaction */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="flex items-center space-x-1">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  <span className="font-semibold">4.8/5</span>
                </div>
                <div className="text-right">
                  <p className="font-medium">1,247+ Happy Customers</p>
                  <p className="text-sm text-gray-600">Join thousands of satisfied buyers</p>
                </div>
              </div>
            </div>
          </div>

          {/* Category hidden */}
          <div className="mb-2 sm:mb-4 hidden lg:block">
            <span className="text-xs sm:text-sm text-gray-500 uppercase tracking-wide">{product.categories?.name || 'Uncategorized'}</span>
          </div>
        </div>
      </div>

      {/* Product Information Tabs */}
      <div className="mt-16 border-t pt-16">
        <div className="max-w-4xl mx-auto">
          {/* Tab Navigation */}
          <div className="flex flex-wrap justify-center lg:justify-start border-b border-gray-200 mb-8">
            {[
              { id: 'description', label: 'Description', icon: MessageCircle },
              { id: 'specifications', label: 'Specifications', icon: Package },
              { id: 'reviews', label: `Reviews (${totalReviews})`, icon: Star },
              { id: 'shipping', label: 'Shipping & Returns', icon: Truck }
            ].map(({ id, label, icon: Icon }) => (
              <button
                key={id}
                onClick={() => setActiveTab(id as any)}
                className={`flex items-center space-x-2 px-6 py-3 text-sm font-medium border-b-2 transition-colors ${
                  activeTab === id
                    ? 'border-black text-black'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span>{label}</span>
              </button>
            ))}
          </div>

          {/* Tab Content */}
          <div className="min-h-[300px]">
            {activeTab === 'description' && (
              <div className="prose max-w-none">
                <h3 className="text-xl font-semibold mb-4">Product Description</h3>
                <p className="text-gray-700 leading-relaxed mb-6">
                  {product.description || 'This premium product offers exceptional quality and value. Crafted with attention to detail and designed to meet the highest standards, it represents the perfect blend of functionality and style.'}
                </p>

                <h4 className="text-lg font-semibold mb-3">Key Features</h4>
                <ul className="space-y-2 text-gray-700">
                  <li className="flex items-start">
                    <Award className="w-5 h-5 text-green-600 mr-2 mt-0.5 flex-shrink-0" />
                    Premium quality materials and construction
                  </li>
                  <li className="flex items-start">
                    <Shield className="w-5 h-5 text-blue-600 mr-2 mt-0.5 flex-shrink-0" />
                    Durable and long-lasting design
                  </li>
                  <li className="flex items-start">
                    <Users className="w-5 h-5 text-purple-600 mr-2 mt-0.5 flex-shrink-0" />
                    Suitable for all users and occasions
                  </li>
                  <li className="flex items-start">
                    <RefreshCw className="w-5 h-5 text-orange-600 mr-2 mt-0.5 flex-shrink-0" />
                    Easy maintenance and care
                  </li>
                </ul>
              </div>
            )}

            {activeTab === 'specifications' && (
              <div>
                <h3 className="text-xl font-semibold mb-6">Product Specifications</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {getProductSpecifications().map((spec, index) => (
                    <div key={index} className="flex justify-between items-center py-3 border-b border-gray-100">
                      <span className="font-medium text-gray-900">{spec.label}</span>
                      <span className="text-gray-600">{spec.value}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'reviews' && (
              <div>
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-xl font-semibold">Customer Reviews</h3>
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-1">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`w-5 h-5 ${
                            i < Math.floor(averageRating)
                              ? 'text-yellow-400 fill-current'
                              : 'text-gray-300'
                          }`}
                        />
                      ))}
                      <span className="text-lg font-semibold ml-2">{averageRating.toFixed(1)}</span>
                    </div>
                    <span className="text-gray-600">({totalReviews} reviews)</span>
                  </div>
                </div>

                <div className="space-y-6">
                  {mockReviews.slice(0, showAllReviews ? mockReviews.length : 2).map((review) => (
                    <div key={review.id} className="border-b border-gray-100 pb-6">
                      <div className="flex items-start justify-between mb-3">
                        <div>
                          <div className="flex items-center space-x-2 mb-1">
                            <span className="font-semibold">{review.user}</span>
                            {review.verified && (
                              <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                                Verified Purchase
                              </span>
                            )}
                          </div>
                          <div className="flex items-center space-x-2">
                            <div className="flex">
                              {[...Array(5)].map((_, i) => (
                                <Star
                                  key={i}
                                  className={`w-4 h-4 ${
                                    i < review.rating
                                      ? 'text-yellow-400 fill-current'
                                      : 'text-gray-300'
                                  }`}
                                />
                              ))}
                            </div>
                            <span className="text-sm text-gray-500">{review.date}</span>
                          </div>
                        </div>
                      </div>
                      <h4 className="font-medium mb-2">{review.title}</h4>
                      <p className="text-gray-700 mb-3">{review.comment}</p>
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <button className="flex items-center space-x-1 hover:text-gray-700">
                          <ThumbsUp className="w-4 h-4" />
                          <span>Helpful ({review.helpful})</span>
                        </button>
                      </div>
                    </div>
                  ))}
                </div>

                {mockReviews.length > 2 && (
                  <button
                    onClick={() => setShowAllReviews(!showAllReviews)}
                    className="mt-6 flex items-center space-x-2 text-black font-medium hover:text-gray-700"
                  >
                    <span>{showAllReviews ? 'Show Less' : 'Show All Reviews'}</span>
                    {showAllReviews ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
                  </button>
                )}
              </div>
            )}

            {activeTab === 'shipping' && (
              <div>
                <h3 className="text-xl font-semibold mb-6">Shipping & Returns</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div>
                    <h4 className="font-semibold mb-4 flex items-center">
                      <Truck className="w-5 h-5 mr-2" />
                      Shipping Information
                    </h4>
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start">
                        <Clock className="w-4 h-4 mr-2 mt-1 text-green-600" />
                        Free shipping on orders over $100
                      </li>
                      <li className="flex items-start">
                        <Clock className="w-4 h-4 mr-2 mt-1 text-blue-600" />
                        Standard delivery: 3-5 business days
                      </li>
                      <li className="flex items-start">
                        <Clock className="w-4 h-4 mr-2 mt-1 text-purple-600" />
                        Express delivery: 1-2 business days
                      </li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-4 flex items-center">
                      <RotateCcw className="w-5 h-5 mr-2" />
                      Return Policy
                    </h4>
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start">
                        <Shield className="w-4 h-4 mr-2 mt-1 text-green-600" />
                        30-day return window
                      </li>
                      <li className="flex items-start">
                        <Shield className="w-4 h-4 mr-2 mt-1 text-blue-600" />
                        Free returns on all orders
                      </li>
                      <li className="flex items-start">
                        <Shield className="w-4 h-4 mr-2 mt-1 text-purple-600" />
                        Easy online return process
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Recently Viewed Products */}
      <div className="mt-16 border-t pt-16">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-2xl font-bold text-gray-900">Recently Viewed</h2>
            <button
              onClick={() => setRecentlyViewed([])}
              className="text-sm text-gray-500 hover:text-gray-700 flex items-center space-x-1"
            >
              <RefreshCw className="w-4 h-4" />
              <span>Clear History</span>
            </button>
          </div>

          {/* Mock recently viewed products */}
          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
            {[...Array(4)].map((_, index) => (
              <div
                key={index}
                className="bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow cursor-pointer border border-gray-100"
                onClick={() => navigate(`/products/sample-${index + 1}`)}
              >
                <div className="aspect-square bg-gray-100">
                  <img
                    src={`https://picsum.photos/200/200?random=${index + 10}`}
                    alt={`Recently viewed ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="p-3">
                  <h3 className="font-medium text-sm text-gray-900 line-clamp-2 mb-1">
                    Sample Product {index + 1}
                  </h3>
                  <p className="text-sm font-semibold text-gray-900">
                    ${(29.99 + index * 10).toFixed(2)}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Enhanced Related Products */}
      {relatedProducts.length > 0 && (
        <div className="mt-20 bg-gradient-to-br from-gray-50 to-gray-100 py-20 -mx-2 sm:-mx-4 px-2 sm:px-4">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <div className="inline-flex items-center space-x-2 bg-black text-white px-4 py-2 rounded-full text-sm font-medium mb-4">
                <Star className="w-4 h-4 fill-current" />
                <span>Recommended for You</span>
              </div>
              <h2 className="text-4xl font-bold text-gray-900 mb-6">You might also like</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                Discover more premium products carefully curated to complement your style and preferences
              </p>
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
              {relatedProducts.map((relatedProduct) => (
                <div
                  key={relatedProduct.id}
                  className="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 cursor-pointer group border border-gray-200 hover:border-gray-300 transform hover:-translate-y-2"
                  onClick={() => navigate(`/products/${relatedProduct.id}`)}
                >
                  <div className="relative">
                    <img
                      src={getImageUrl(relatedProduct.image_url)}
                      alt={relatedProduct.name}
                      className="w-full h-56 object-cover group-hover:scale-105 transition-transform duration-300"
                      onError={(e) => {
                        (e.target as HTMLImageElement).src = 'https://via.placeholder.com/300x400?text=No+Image';
                      }}
                    />
                    
                    {/* Stock Badge */}
                    {relatedProduct.stock_quantity <= 5 && relatedProduct.stock_quantity > 0 && (
                      <span className="absolute top-3 left-3 bg-orange-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                        Only {relatedProduct.stock_quantity} left
                      </span>
                    )}
                    
                    {relatedProduct.stock_quantity === 0 && (
                      <span className="absolute top-3 left-3 bg-red-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                        Out of Stock
                      </span>
                    )}
                    
                    {/* Category Badge */}
                    <span className="absolute top-3 right-3 bg-black/80 text-white text-xs px-2 py-1 rounded-full">
                      {relatedProduct.categories?.name || 'Uncategorized'}
                    </span>
                    
                    {/* Quick Actions */}
                    <div className="absolute bottom-3 right-3 flex flex-col space-y-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <button 
                        className="bg-white p-2 rounded-full shadow-lg hover:bg-gray-100 transition-colors"
                        onClick={e => {e.stopPropagation(); /* Add to wishlist */}}
                      >
                        <Heart className="w-4 h-4" />
                      </button>
                      <button 
                        className="bg-black text-white p-2 rounded-full shadow-lg hover:bg-gray-800 transition-colors disabled:opacity-50"
                        onClick={e => {e.stopPropagation(); handleAddRelatedToCart(relatedProduct);}}
                        disabled={relatedProduct.stock_quantity === 0}
                      >
                        <ShoppingBag className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                  
                  <div className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-xs text-gray-500 uppercase tracking-wide">{relatedProduct.categories?.name || 'Uncategorized'}</span>
                    </div>
                    
                    <h3 className="font-semibold text-gray-900 mb-2 group-hover:text-black transition-colors line-clamp-2">
                      {relatedProduct.name}
                    </h3>
                    
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-2">
                        <span className="font-bold text-lg text-gray-900">{formatPrice(relatedProduct.price)}</span>
                      </div>
                    </div>
                    
                    {/* Show available variants */}
                    {(relatedProduct.sizes && relatedProduct.sizes.length > 0) || (relatedProduct.colors && relatedProduct.colors.length > 0) ? (
                      <div className="mb-3 text-xs text-gray-500">
                        {relatedProduct.sizes && relatedProduct.sizes.length > 0 && (
                          <span className="mr-2">Sizes: {relatedProduct.sizes.slice(0, 2).join(', ')}{relatedProduct.sizes.length > 2 && '...'}</span>
                        )}
                        {relatedProduct.colors && relatedProduct.colors.length > 0 && (
                          <span>Colors: {relatedProduct.colors.slice(0, 2).join(', ')}{relatedProduct.colors.length > 2 && '...'}</span>
                        )}
                      </div>
                    ) : null}
                    
                    <div className="flex space-x-2">
                      <button
                        className="flex-1 bg-black text-white py-2 px-3 rounded-lg text-sm font-medium hover:bg-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                        onClick={e => {e.stopPropagation(); handleAddRelatedToCart(relatedProduct);}}
                        disabled={relatedProduct.stock_quantity === 0}
                      >
                        Add to Cart
                      </button>
                      <button
                        className="flex-1 bg-gray-100 text-black py-2 px-3 rounded-lg text-sm font-medium hover:bg-gray-200 transition-colors"
                        onClick={e => {e.stopPropagation(); navigate(`/products/${relatedProduct.id}`);}}
                      >
                        View
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="text-center mt-16">
              <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-200 max-w-md mx-auto">
                <h3 className="text-xl font-bold text-gray-900 mb-3">Explore Our Full Collection</h3>
                <p className="text-gray-600 mb-6">Discover thousands of premium products across all categories</p>
                <button
                  className="bg-gradient-to-r from-black to-gray-800 text-white px-8 py-4 rounded-xl font-semibold hover:from-gray-800 hover:to-black transition-all duration-200 transform hover:scale-105 shadow-lg"
                  onClick={() => navigate('/products')}
                >
                  View All Products
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductDetailPage; 