# Database Migration Guide

## Issue
The error "Could not find the 'payment_status' column of 'orders' in the schema cache" occurs because the `payment_status` column is missing from your `orders` table in Supabase.

Additionally, if you tried to run `007_stripe_integration.sql`, you may have encountered the error:
```
ERROR: 42883: operator does not exist: uuid = text
HINT: No operator matches the given name and argument types. You might need to add explicit type casts.
```

## Solution
You need to apply the database migration to add the missing column. I've created a fixed version that handles the UUID casting issue.

## Step 1: Access Your Supabase Dashboard

1. Go to [Supabase Dashboard](https://supabase.com/dashboard)
2. Select your project
3. Go to **SQL Editor** in the left sidebar

## Step 2: Run the Fixed Migration

Copy and paste the following SQL into the SQL Editor and click "Run":

```sql
-- Migration: Add payment_status column to orders table (FIXED VERSION)
-- This migration adds the missing payment_status column that's required for checkout

-- Add payment_status column to orders table
ALTER TABLE orders ADD COLUMN IF NOT EXISTS payment_status TEXT DEFAULT 'pending';

-- Add stripe_payment_intent_id column for Stripe integration
ALTER TABLE orders ADD COLUMN IF NOT EXISTS stripe_payment_intent_id TEXT;

-- Create index for payment_status for better performance
CREATE INDEX IF NOT EXISTS idx_orders_payment_status ON orders(payment_status);

-- Update the status check constraint to include payment-related statuses
ALTER TABLE orders DROP CONSTRAINT IF EXISTS orders_status_check;
ALTER TABLE orders ADD CONSTRAINT orders_status_check 
    CHECK (status IN ('pending', 'processing', 'shipped', 'delivered', 'cancelled', 'paid', 'failed'));

-- Add comment to document the payment_status column
COMMENT ON COLUMN orders.payment_status IS 'Payment status: pending, paid, failed, refunded';
COMMENT ON COLUMN orders.stripe_payment_intent_id IS 'Stripe payment intent ID for tracking payments';
```

## Step 3: Apply Stripe Integration (Optional)

If you want to set up full Stripe integration, run this additional migration:

```sql
-- Migration: Add Stripe integration tables (FIXED VERSION)
-- This migration adds tables for handling Stripe payments and webhooks

-- Create payments table
CREATE TABLE IF NOT EXISTS payments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    stripe_payment_intent_id TEXT UNIQUE NOT NULL,
    order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
    customer_id UUID REFERENCES customers(id) ON DELETE CASCADE,
    amount INTEGER NOT NULL, -- Amount in cents
    currency TEXT DEFAULT 'usd',
    status TEXT NOT NULL DEFAULT 'pending',
    payment_method TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create payment_methods table
CREATE TABLE IF NOT EXISTS payment_methods (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    customer_id UUID REFERENCES customers(id) ON DELETE CASCADE,
    stripe_payment_method_id TEXT UNIQUE NOT NULL,
    type TEXT NOT NULL,
    card_brand TEXT,
    card_last4 TEXT,
    card_exp_month INTEGER,
    card_exp_year INTEGER,
    is_default BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create webhook_events table for tracking Stripe webhooks
CREATE TABLE IF NOT EXISTS webhook_events (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    stripe_event_id TEXT UNIQUE NOT NULL,
    event_type TEXT NOT NULL,
    event_data JSONB NOT NULL,
    processed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_payments_stripe_payment_intent_id ON payments(stripe_payment_intent_id);
CREATE INDEX IF NOT EXISTS idx_payments_order_id ON payments(order_id);
CREATE INDEX IF NOT EXISTS idx_payments_customer_id ON payments(customer_id);
CREATE INDEX IF NOT EXISTS idx_payments_status ON payments(status);

CREATE INDEX IF NOT EXISTS idx_payment_methods_customer_id ON payment_methods(customer_id);
CREATE INDEX IF NOT EXISTS idx_payment_methods_stripe_payment_method_id ON payment_methods(stripe_payment_method_id);

CREATE INDEX IF NOT EXISTS idx_webhook_events_stripe_event_id ON webhook_events(stripe_event_id);
CREATE INDEX IF NOT EXISTS idx_webhook_events_event_type ON webhook_events(event_type);
CREATE INDEX IF NOT EXISTS idx_webhook_events_processed ON webhook_events(processed);

-- Add RLS policies for payments table
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;

-- Allow customers to view their own payments (FIXED: cast customer_id to text for comparison)
CREATE POLICY "Customers can view their own payments" ON payments
    FOR SELECT USING (customer_id::text = auth.uid());

-- Allow authenticated users to insert payments
CREATE POLICY "Authenticated users can insert payments" ON payments
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Allow system to update payments (for webhooks)
CREATE POLICY "System can update payments" ON payments
    FOR UPDATE USING (true);

-- Add RLS policies for payment_methods table
ALTER TABLE payment_methods ENABLE ROW LEVEL SECURITY;

-- Allow customers to view their own payment methods (FIXED: cast customer_id to text for comparison)
CREATE POLICY "Customers can view their own payment methods" ON payment_methods
    FOR SELECT USING (customer_id::text = auth.uid());

-- Allow customers to insert their own payment methods (FIXED: cast customer_id to text for comparison)
CREATE POLICY "Customers can insert their own payment methods" ON payment_methods
    FOR INSERT WITH CHECK (customer_id::text = auth.uid());

-- Allow customers to update their own payment methods (FIXED: cast customer_id to text for comparison)
CREATE POLICY "Customers can update their own payment methods" ON payment_methods
    FOR UPDATE USING (customer_id::text = auth.uid());

-- Allow customers to delete their own payment methods (FIXED: cast customer_id to text for comparison)
CREATE POLICY "Customers can delete their own payment methods" ON payment_methods
    FOR DELETE USING (customer_id::text = auth.uid());

-- Add RLS policies for webhook_events table
ALTER TABLE webhook_events ENABLE ROW LEVEL SECURITY;

-- Only allow service role to access webhook events
CREATE POLICY "Service role can manage webhook events" ON webhook_events
    FOR ALL USING (auth.role() = 'service_role');

-- Create function to update order payment status
CREATE OR REPLACE FUNCTION update_order_payment_status()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.status = 'paid' THEN
        UPDATE orders 
        SET payment_status = 'paid', 
            stripe_payment_intent_id = NEW.stripe_payment_intent_id,
            updated_at = NOW()
        WHERE id = NEW.order_id;
    ELSIF NEW.status = 'failed' THEN
        UPDATE orders 
        SET payment_status = 'failed',
            updated_at = NOW()
        WHERE id = NEW.order_id;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for payment status updates
CREATE TRIGGER payment_status_trigger
    AFTER UPDATE ON payments
    FOR EACH ROW
    EXECUTE FUNCTION update_order_payment_status();
```

## Step 4: Verify the Migration

After running the migration, verify that the column was added:

```sql
-- Check if the payment_status column exists
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'orders' AND column_name = 'payment_status';
```

## Step 5: Test the Application

1. Refresh your application
2. Try to access the checkout page again
3. The error should be resolved

## What Was Fixed

The original `007_stripe_integration.sql` had a UUID casting issue in the RLS policies:
- **Problem**: `customer_id = auth.uid()::text` (comparing UUID with text)
- **Solution**: `customer_id::text = auth.uid()` (casting UUID to text for comparison)

## Alternative: Apply All Migrations

If you want to apply all migrations at once, you can run them in order:

1. **001_initial_schema.sql** - Basic tables
2. **002_sample_data.sql** - Sample data
3. **003_storage_policies.sql** - Storage policies
4. **004_add_product_variants.sql** - Product variants
5. **005_fix_storage_policies.sql** - Fix storage policies
6. **006_add_product_gallery.sql** - Product gallery
7. **007_stripe_integration_fixed.sql** - Stripe integration (FIXED VERSION)
8. **008_add_payment_status.sql** - Payment status (redundant if you ran the fixed version)

## Troubleshooting

### If you get a constraint error:
The migration includes updating the status constraint. If you get an error about existing data, you may need to update existing orders first:

```sql
-- Update existing orders to have valid status
UPDATE orders SET status = 'pending' WHERE status NOT IN ('pending', 'processing', 'shipped', 'delivered', 'cancelled', 'paid', 'failed');
```

### If the column already exists:
The migration uses `IF NOT EXISTS`, so it's safe to run even if the column already exists.

### If you need to rollback:
```sql
-- Remove the payment_status column (only if needed)
ALTER TABLE orders DROP COLUMN IF EXISTS payment_status;
ALTER TABLE orders DROP COLUMN IF EXISTS stripe_payment_intent_id;
```

## Next Steps

After applying this migration:
1. The checkout process should work without the column error
2. You can proceed with setting up Stripe integration
3. Payment status tracking will be available
4. Guest checkout functionality can be implemented

## Support

If you continue to have issues:
1. Check the Supabase logs for any SQL errors
2. Verify your database permissions
3. Make sure you're running the SQL in the correct project
4. Contact Supabase support if needed 