# Gallery Setup Instructions

The product gallery functionality has been added to the admin interface, but you need to add the `gallery` column to your database to store the gallery images.

## Option 1: Using Supabase Dashboard (Recommended)

1. Go to your Supabase project dashboard
2. Navigate to the SQL Editor
3. Run the following SQL command:

```sql
-- Add gallery field to products table
ALTER TABLE products 
ADD COLUMN gallery TEXT[] DEFAULT NULL;

-- Add comment to explain the gallery field
COMMENT ON COLUMN products.gallery IS 'Array of image URLs for product gallery (e.g., ["url1", "url2", "url3"])';

-- Update existing products to have their main image in the gallery
UPDATE products 
SET gallery = ARRAY[image_url]
WHERE gallery IS NULL AND image_url IS NOT NULL;
```

## Option 2: Using Supabase CLI

If you have the Supabase CLI installed:

1. Run the migration file:
```bash
supabase db push
```

## Option 3: Manual Database Update

If you have direct database access, you can run the SQL commands directly in your database management tool.

## After Adding the Column

Once you've added the gallery column:

1. The admin interface will automatically start saving gallery images to the database
2. The product detail page will display the gallery images
3. You can edit existing products to add gallery images

## Current Status

- ✅ Gallery upload interface is working
- ✅ Images are being uploaded to storage
- ✅ Product detail page can display gallery images
- ⏳ Database column needs to be added to store gallery URLs

## Troubleshooting

If you see "Failed to save product" errors:
1. Check that the gallery column exists in your database
2. Verify your Supabase permissions allow adding columns
3. Check the browser console for detailed error messages

## Features Added

1. **Admin Products Page:**
   - Multiple image upload for product gallery
   - Preview of uploaded images
   - Ability to remove individual images
   - Drag and drop support

2. **Product Detail Page:**
   - Gallery image display with navigation arrows
   - Thumbnail navigation
   - Image counter
   - Smooth transitions between images

3. **File Upload Service:**
   - Multiple file validation
   - Gallery-specific upload folder
   - Error handling for upload failures 