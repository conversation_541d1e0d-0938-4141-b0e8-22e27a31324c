{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"setup": "node setup.js", "dev": "vite", "dev:client": "vite", "dev:server": "cd server && npm run dev", "dev:all": "concurrently \"npm run dev:client\" \"npm run dev:server\"", "build": "vite build", "build:server": "cd server && npm install", "build:all": "npm run build && npm run build:server", "start:server": "cd server && npm start", "start:all": "concurrently \"npm run preview\" \"npm run start:server\"", "install:all": "npm install && cd server && npm install", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.4.0", "@supabase/supabase-js": "^2.50.2", "@types/react-router-dom": "^5.3.3", "@types/recharts": "^1.8.29", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.6.2", "recharts": "^3.0.2", "stripe": "^18.2.1", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "concurrently": "^8.2.2", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}