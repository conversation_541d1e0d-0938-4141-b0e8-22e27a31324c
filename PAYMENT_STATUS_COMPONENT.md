# Payment Status Component

A comprehensive React component for displaying payment status information with detailed feedback for users.

## Features

- **Multiple Status Types**: Supports success, failed, cancelled, and pending payment states
- **Detailed Error Analysis**: Automatically analyzes error messages and provides specific solutions
- **Payment Information Display**: Shows payment details, order information, and transaction history
- **Responsive Design**: Works seamlessly on desktop and mobile devices
- **Action Buttons**: Provides appropriate actions based on payment status
- **Support Information**: Includes contact information for failed payments

## Usage

### Basic Usage

```tsx
import PaymentStatusComponent from './components/PaymentStatusComponent';

<PaymentStatusComponent
  status="success"
  paymentIntent={paymentIntent}
  orderId="ORD-123456"
  total={99.99}
  orderItems={orderItems}
  onRetry={handleRetry}
  onBack={handleBack}
/>
```

### Props

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `status` | `'success' \| 'failed' \| 'pending' \| 'cancelled'` | Yes | The payment status to display |
| `paymentIntent` | `any` | No | Stripe payment intent object with payment details |
| `orderId` | `string` | No | The order ID for reference |
| `total` | `number` | No | The total amount paid |
| `orderItems` | `any[]` | No | Array of items in the order |
| `errorMessage` | `string` | No | Error message for failed payments |
| `onRetry` | `() => void` | No | Callback function for retry action |
| `onBack` | `() => void` | No | Callback function for back action |

### Status Types

#### Success
- Green color scheme
- Shows payment confirmation
- Displays order summary
- Provides next steps information
- Action buttons: Continue Shopping, View Orders

#### Failed
- Red color scheme
- Shows error details with specific solutions
- Displays payment information if available
- Provides retry options
- Action buttons: Try Again, Return to Cart
- Includes support contact information

#### Cancelled
- Yellow color scheme
- Shows cancellation message
- Provides retry options
- Action buttons: Try Again, Return to Cart

#### Pending
- Blue color scheme
- Shows processing message
- Minimal information display

### Error Analysis

The component automatically analyzes common payment error messages and provides specific solutions:

- **Card Declined**: Suggests checking with bank, verifying funds, trying different payment method
- **Insufficient Funds**: Suggests checking balance, trying different payment method, contacting bank
- **Expired Card**: Suggests using different card, updating information, contacting bank
- **Invalid CVC**: Suggests double-checking security code
- **Network Error**: Suggests checking internet connection, trying again, contacting support

### Integration Examples

#### In Checkout Page

```tsx
const CheckoutPage = () => {
  const [paymentStatus, setPaymentStatus] = useState('pending');
  const [paymentIntent, setPaymentIntent] = useState(null);

  const handlePaymentSuccess = (paymentIntent) => {
    setPaymentStatus('success');
    setPaymentIntent(paymentIntent);
  };

  const handlePaymentError = (error) => {
    setPaymentStatus('failed');
    setError(error);
  };

  if (paymentStatus === 'success' || paymentStatus === 'failed') {
    return (
      <PaymentStatusComponent
        status={paymentStatus}
        paymentIntent={paymentIntent}
        orderId={orderId}
        total={total}
        orderItems={orderItems}
        errorMessage={error}
        onRetry={handleRetry}
        onBack={handleBack}
      />
    );
  }

  // ... rest of checkout form
};
```

#### Standalone Pages

```tsx
// PaymentFailurePage.tsx
const PaymentFailurePage = () => {
  const location = useLocation();
  const { paymentIntent, orderId, total, orderItems, errorMessage } = location.state || {};

  return (
    <PaymentStatusComponent
      status="failed"
      paymentIntent={paymentIntent}
      orderId={orderId}
      total={total}
      orderItems={orderItems}
      errorMessage={errorMessage}
      onRetry={() => navigate('/checkout')}
      onBack={() => navigate('/cart')}
    />
  );
};
```

## Demo

Visit `/payment-status-demo` to see all payment status states in action. The demo page allows you to switch between different status types to see how the component behaves.

## Styling

The component uses Tailwind CSS classes and follows the existing design system. It's fully responsive and maintains consistency with the rest of the application.

## Customization

The component is designed to be flexible and can be customized by:

1. **Modifying error patterns**: Add new error patterns in the `getErrorDetails` function
2. **Customizing colors**: Update the `getStatusConfig` function to change color schemes
3. **Adding new actions**: Extend the action buttons section for additional functionality
4. **Modifying layout**: Adjust the component structure to match your design requirements

## Dependencies

- React
- React Router (for navigation)
- Lucide React (for icons)
- Tailwind CSS (for styling)

## Browser Support

The component works in all modern browsers that support:
- ES6+ features
- CSS Grid and Flexbox
- CSS Custom Properties 