import { supabase, Product } from '../lib/supabase';

export interface CreateProductData {
  name: string;
  description: string;
  price: number;
  image_url: string;
  category_id: string;
  stock_quantity: number;
  is_featured?: boolean;
  sizes?: string[];
  colors?: string[];
  gallery?: string[];
}

export interface UpdateProductData extends Partial<CreateProductData> {
  id: string;
}

export class ProductService {
  // Get all products with optional filtering
  static async getProducts(filters?: {
    category_id?: string;
    is_featured?: boolean;
    search?: string;
    limit?: number;
    offset?: number;
  }) {
    let query = supabase
      .from('products')
      .select(`
        *,
        categories (
          id,
          name,
          description
        )
      `);

    if (filters?.category_id) {
      query = query.eq('category_id', filters.category_id);
    }

    if (filters?.is_featured !== undefined) {
      query = query.eq('is_featured', filters.is_featured);
    }

    if (filters?.search) {
      query = query.or(`name.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
    }

    if (filters?.limit) {
      query = query.limit(filters.limit);
    }

    if (filters?.offset) {
      query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1);
    }

    query = query.order('created_at', { ascending: false });

    const { data, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch products: ${error.message}`);
    }

    return data;
  }

  // Get a single product by ID
  static async getProductById(id: string) {
    const { data, error } = await supabase
      .from('products')
      .select(`
        *,
        categories (
          id,
          name,
          description
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      throw new Error(`Failed to fetch product: ${error.message}`);
    }

    return data;
  }

  // Get featured products
  static async getFeaturedProducts(limit = 8) {
    return this.getProducts({ is_featured: true, limit });
  }

  // Create a new product
  static async createProduct(productData: CreateProductData) {
    const { data, error } = await supabase
      .from('products')
      .insert([productData])
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create product: ${error.message}`);
    }

    return data;
  }

  // Update a product
  static async updateProduct(productData: UpdateProductData) {
    const { id, ...updateData } = productData;
    
    const { data, error } = await supabase
      .from('products')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update product: ${error.message}`);
    }

    return data;
  }

  // Delete a product
  static async deleteProduct(id: string) {
    const { error } = await supabase
      .from('products')
      .delete()
      .eq('id', id);

    if (error) {
      throw new Error(`Failed to delete product: ${error.message}`);
    }

    return true;
  }

  // Update stock quantity
  static async updateStock(id: string, quantity: number) {
    const { data, error } = await supabase
      .from('products')
      .update({ stock_quantity: quantity })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update stock: ${error.message}`);
    }

    return data;
  }

  // Get products by category
  static async getProductsByCategory(categoryId: string, limit?: number) {
    return this.getProducts({ category_id: categoryId, limit });
  }

  // Search products
  static async searchProducts(searchTerm: string, limit = 20) {
    return this.getProducts({ search: searchTerm, limit });
  }

  // Get low stock products (for admin alerts)
  static async getLowStockProducts(threshold = 10) {
    const { data, error } = await supabase
      .from('products')
      .select('*')
      .lte('stock_quantity', threshold)
      .order('stock_quantity', { ascending: true });

    if (error) {
      throw new Error(`Failed to fetch low stock products: ${error.message}`);
    }

    return data;
  }
}
