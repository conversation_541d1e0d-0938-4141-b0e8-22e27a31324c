# API Server for E-commerce Store

This is the backend API server for the e-commerce store with Stripe payment integration.

## Features

- **Stripe Payment Processing**: Complete payment intent creation and management
- **Customer Management**: Create and manage Stripe customers
- **Payment Methods**: Save, retrieve, and delete payment methods
- **Webhook Handling**: Process Stripe webhook events
- **Security**: CORS protection, helmet security headers, input validation
- **Logging**: Request logging with Morgan

## Setup

### 1. Install Dependencies

```bash
npm install
```

### 2. Environment Configuration

Copy the example environment file and configure it:

```bash
cp env.example .env
```

Edit `.env` with your actual values:

```env
NODE_ENV=development
PORT=3001
FRONTEND_URL=http://localhost:5173
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
```

### 3. Get Stripe Keys

1. Go to [Stripe Dashboard](https://dashboard.stripe.com)
2. Navigate to **Developers** > **API keys**
3. Copy your **Secret key** (starts with `sk_test_` for testing)
4. For webhooks, go to **Developers** > **Webhooks**
5. Create a webhook endpoint pointing to `http://localhost:3001/api/stripe/webhook`
6. Copy the **Webhook signing secret** (starts with `whsec_`)

## Running the Server

### Development Mode
```bash
npm run dev
```

### Production Mode
```bash
npm start
```

The server will start on port 3001 (or the port specified in your .env file).

## API Endpoints

### Health Check
- `GET /api/health` - Server health status

### Stripe Endpoints
- `POST /api/stripe/create-payment-intent` - Create a payment intent
- `POST /api/stripe/confirm-payment` - Confirm a payment
- `POST /api/stripe/create-customer` - Create a Stripe customer
- `POST /api/stripe/save-payment-method` - Save a payment method
- `GET /api/stripe/payment-methods/:customerId` - Get customer's payment methods
- `DELETE /api/stripe/payment-methods/:paymentMethodId` - Delete a payment method
- `GET /api/stripe/payment-history/:customerId` - Get payment history
- `POST /api/stripe/webhook` - Stripe webhook endpoint

## Testing

### Test the Health Endpoint
```bash
curl http://localhost:3001/api/health
```

### Test Payment Intent Creation
```bash
curl -X POST http://localhost:3001/api/stripe/create-payment-intent \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 2000,
    "currency": "usd",
    "customer_id": "cus_test123",
    "order_id": "order_123"
  }'
```

## Webhook Testing

For local webhook testing, you can use Stripe CLI:

1. Install Stripe CLI: https://stripe.com/docs/stripe-cli
2. Login: `stripe login`
3. Forward webhooks: `stripe listen --forward-to localhost:3001/api/stripe/webhook`

## Security

- CORS is configured to only allow requests from the frontend URL
- Helmet provides security headers
- Input validation on all endpoints
- Webhook signature verification
- Environment variables for sensitive data

## Error Handling

The server includes comprehensive error handling:
- Input validation errors (400)
- Stripe API errors (400)
- Internal server errors (500)
- Not found errors (404)

All errors are logged to the console for debugging.

## Logging

The server uses Morgan for HTTP request logging. You'll see logs like:
```
::1 - - [28/Jun/2025:22:37:00 +0000] "GET /api/health HTTP/1.1" 200 123
```

## Production Deployment

For production deployment:

1. Set `NODE_ENV=production`
2. Use production Stripe keys
3. Configure proper CORS origins
4. Set up SSL/TLS
5. Use a process manager like PM2
6. Set up proper logging and monitoring

## Troubleshooting

### Common Issues

1. **CORS errors**: Check that `FRONTEND_URL` is set correctly
2. **Stripe errors**: Verify your Stripe secret key is correct
3. **Port already in use**: Change the `PORT` in your .env file
4. **Webhook not working**: Check webhook secret and endpoint URL

### Debug Mode

To enable more detailed logging, set `NODE_ENV=development` in your .env file. 