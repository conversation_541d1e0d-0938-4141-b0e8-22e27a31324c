import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import PaymentStatusComponent from '../components/PaymentStatusComponent';

const PaymentCancelledPage = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { 
    orderId, 
    total, 
    orderItems 
  } = location.state || {};

  const handleRetry = () => {
    // Navigate back to checkout to retry payment
    navigate('/checkout', { 
      state: { 
        cartItems: orderItems,
        total: total
      } 
    });
  };

  const handleBack = () => {
    navigate('/cart');
  };

  return (
    <PaymentStatusComponent
      status="cancelled"
      orderId={orderId}
      total={total}
      orderItems={orderItems}
      onRetry={handleRetry}
      onBack={handleBack}
    />
  );
};

export default PaymentCancelledPage; 