# Environment Variables Setup

To fix the Stripe checkout errors, you need to set up the following environment variables:

## Frontend Environment Variables (.env file in root directory)

Create a `.env` file in the root directory of your project with the following variables:

```env
# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_url_here
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Stripe Configuration
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key_here

# API Configuration
VITE_API_BASE_URL=http://localhost:3001/api
```

## Backend Environment Variables (.env file in server directory)

Create a `.env` file in the `server` directory with the following variables:

```env
# Server Environment Variables
NODE_ENV=development
PORT=3001

# Frontend URL (for CORS)
FRONTEND_URL=http://localhost:5173

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_51ReoYrFRBfWA0dsqKwynq01fTWIvvrT4ifcPdLekK2kX8Wj33YKZit3HE6Tu2lQQafzuflnW88167thPRvB5TXDY00CWK49xt9
```

## How to Get Your Keys:

1. **Stripe Keys**: 
   - Go to your Stripe Dashboard
   - Navigate to Developers > API Keys
   - Copy your Publishable Key (starts with `pk_test_`) for `VITE_STRIPE_PUBLISHABLE_KEY`
   - Copy your Secret Key (starts with `sk_test_`) for `STRIPE_SECRET_KEY`

2. **Supabase Keys**:
   - Go to your Supabase Dashboard
   - Navigate to Settings > API
   - Copy your Project URL for `VITE_SUPABASE_URL`
   - Copy your anon/public key for `VITE_SUPABASE_ANON_KEY`

## Starting the Application:

1. Start the backend server:
   ```bash
   cd server
   npm start
   ```

2. Start the frontend:
   ```bash
   npm run dev
   ```

## Troubleshooting:

- Make sure both servers are running (frontend on port 5173, backend on port 3001)
- Check the browser console for any error messages
- Verify that your Stripe keys are correct and in test mode
- Ensure your Supabase configuration is correct 