import { loadStripe, <PERSON>e } from '@stripe/stripe-js';

// Initialize Stripe with error handling
const getStripePromise = () => {
  const publishableKey = import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY;
  if (!publishableKey || publishableKey === 'pk_test_your_publishable_key_here') {
    console.warn('Stripe publishable key not configured. Please set VITE_STRIPE_PUBLISHABLE_KEY in your .env file.');
    return null;
  }
  return loadStripe(publishableKey);
};

const stripePromise = getStripePromise();

export interface PaymentIntent {
  id: string;
  amount: number;
  currency: string;
  status: string;
  client_secret: string;
}

export interface PaymentMethod {
  id: string;
  type: string;
  card?: {
    brand: string;
    last4: string;
    exp_month: number;
    exp_year: number;
  };
}

export interface CreatePaymentIntentData {
  amount: number; // Amount in cents
  currency?: string;
  customer_id?: string;
  order_id?: string;
  metadata?: Record<string, string>;
}

export interface SavePaymentMethodData {
  customer_id: string;
  payment_method_id: string;
  is_default?: boolean;
}

export interface Customer {
  id: string;
  email: string;
  name: string;
  phone?: string;
  address?: {
    line1: string;
    city: string;
    state: string;
    postal_code: string;
    country: string;
  };
}

class StandaloneStripeService {
  private stripe: Stripe | null = null;
  private apiBaseUrl: string;

  constructor() {
    // Use environment variable or default to local server
    this.apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api';
    console.log('API Base URL:', this.apiBaseUrl);
  }

  async initialize() {
    if (!this.stripe) {
      this.stripe = await stripePromise;
    }
    return this.stripe;
  }

  // Create a payment intent using your own API endpoint
  async createPaymentIntent(data: CreatePaymentIntentData): Promise<PaymentIntent> {
    try {
      console.log('Creating payment intent with:', data);
      
      const response = await fetch(`${this.apiBaseUrl}/stripe/create-payment-intent`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: data.amount,
          currency: data.currency || 'usd',
          customer_id: data.customer_id,
          order_id: data.order_id,
          metadata: data.metadata || {}
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Failed to create payment intent: ${errorData.message || response.statusText}`);
      }

      const paymentIntent = await response.json();
      return paymentIntent;
    } catch (error) {
      console.error('Error creating payment intent:', error);
      throw error;
    }
  }

  // Confirm payment with payment method
  async confirmPayment(paymentIntentId: string, paymentMethodId: string) {
    try {
      const response = await fetch(`${this.apiBaseUrl}/stripe/confirm-payment`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          payment_intent_id: paymentIntentId,
          payment_method_id: paymentMethodId
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Failed to confirm payment: ${errorData.message || response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error confirming payment:', error);
      throw error;
    }
  }

  // Save payment method for future use
  async savePaymentMethod(data: SavePaymentMethodData) {
    try {
      const response = await fetch(`${this.apiBaseUrl}/stripe/save-payment-method`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          customer_id: data.customer_id,
          payment_method_id: data.payment_method_id,
          is_default: data.is_default || false
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Failed to save payment method: ${errorData.message || response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error saving payment method:', error);
      throw error;
    }
  }

  // Get customer's saved payment methods
  async getPaymentMethods(customerId: string) {
    try {
      const response = await fetch(`${this.apiBaseUrl}/stripe/payment-methods/${customerId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Failed to get payment methods: ${errorData.message || response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error getting payment methods:', error);
      throw error;
    }
  }

  // Delete a payment method
  async deletePaymentMethod(paymentMethodId: string) {
    try {
      const response = await fetch(`${this.apiBaseUrl}/stripe/payment-methods/${paymentMethodId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Failed to delete payment method: ${errorData.message || response.statusText}`);
      }

      return true;
    } catch (error) {
      console.error('Error deleting payment method:', error);
      throw error;
    }
  }

  // Get payment history for a customer
  async getPaymentHistory(customerId: string) {
    try {
      const response = await fetch(`${this.apiBaseUrl}/stripe/payment-history/${customerId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Failed to get payment history: ${errorData.message || response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error getting payment history:', error);
      throw error;
    }
  }

  // Create a customer in Stripe
  async createCustomer(customerData: {
    email: string;
    name: string;
    phone?: string;
    address?: {
      line1: string;
      city: string;
      state: string;
      postal_code: string;
      country: string;
    };
  }) {
    try {
      const response = await fetch(`${this.apiBaseUrl}/stripe/create-customer`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(customerData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Failed to create customer: ${errorData.message || response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating customer:', error);
      throw error;
    }
  }

  // Get Stripe instance
  async getStripe(): Promise<Stripe | null> {
    return await this.initialize();
  }

  // Format amount for display (convert from cents)
  formatAmount(amount: number, currency: string = 'usd'): string {
    const formatter = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    });
    return formatter.format(amount / 100);
  }

  // Validate card number (basic Luhn algorithm)
  validateCardNumber(cardNumber: string): boolean {
    const cleaned = cardNumber.replace(/\s/g, '');
    if (!/^\d+$/.test(cleaned)) return false;
    
    let sum = 0;
    let isEven = false;
    
    for (let i = cleaned.length - 1; i >= 0; i--) {
      let digit = parseInt(cleaned[i]);
      
      if (isEven) {
        digit *= 2;
        if (digit > 9) {
          digit -= 9;
        }
      }
      
      sum += digit;
      isEven = !isEven;
    }
    
    return sum % 10 === 0;
  }

  // Validate expiration date
  validateExpirationDate(expMonth: string, expYear: string): boolean {
    const month = parseInt(expMonth);
    const year = parseInt(expYear);
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1;
    
    if (month < 1 || month > 12) return false;
    if (year < currentYear) return false;
    if (year === currentYear && month < currentMonth) return false;
    
    return true;
  }

  // Validate CVC
  validateCVC(cvc: string): boolean {
    const cleaned = cvc.replace(/\s/g, '');
    return /^\d{3,4}$/.test(cleaned);
  }

  // Process payment using Stripe Elements
  async processPayment(paymentIntentId: string, paymentMethodId: string) {
    try {
      const stripe = await this.initialize();
      if (!stripe) {
        throw new Error('Stripe not initialized');
      }

      const { error } = await stripe.confirmCardPayment(paymentIntentId, {
        payment_method: paymentMethodId,
      });

      if (error) {
        throw error;
      }

      return { success: true };
    } catch (error) {
      console.error('Error processing payment:', error);
      throw error;
    }
  }

  // Setup payment method for future use
  async setupPaymentMethod(paymentMethodId: string, customerId: string) {
    try {
      const stripe = await this.initialize();
      if (!stripe) {
        throw new Error('Stripe not initialized');
      }

      const { error } = await stripe.confirmCardSetup(paymentMethodId, {
        payment_method: paymentMethodId,
      });

      if (error) {
        throw error;
      }

      return { success: true };
    } catch (error) {
      console.error('Error setting up payment method:', error);
      throw error;
    }
  }
}

// Export a singleton instance
export const standaloneStripeService = new StandaloneStripeService(); 