-- Insert sample categories
INSERT INTO categories (name, description, image_url) VALUES
('Electronics', 'Latest electronic devices and gadgets', 'https://images.unsplash.com/photo-1498049794561-7780e7231661?w=500'),
('Clothing', 'Fashion and apparel for all occasions', 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=500'),
('Home & Garden', 'Everything for your home and garden', 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=500'),
('Sports', 'Sports equipment and fitness gear', 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=500'),
('Books', 'Books and educational materials', 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=500');

-- Insert sample products
INSERT INTO products (name, description, price, image_url, category_id, stock_quantity, is_featured) VALUES
-- Electronics
('Wireless Headphones', 'High-quality wireless headphones with noise cancellation', 199.99, 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500', (SELECT id FROM categories WHERE name = 'Electronics'), 50, true),
('Smartphone', 'Latest smartphone with advanced features', 699.99, 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=500', (SELECT id FROM categories WHERE name = 'Electronics'), 30, true),
('Laptop', 'Powerful laptop for work and gaming', 1299.99, 'https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=500', (SELECT id FROM categories WHERE name = 'Electronics'), 20, false),
('Tablet', 'Lightweight tablet for productivity', 399.99, 'https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?w=500', (SELECT id FROM categories WHERE name = 'Electronics'), 40, false),

-- Clothing
('Cotton T-Shirt', 'Comfortable cotton t-shirt in various colors', 29.99, 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=500', (SELECT id FROM categories WHERE name = 'Clothing'), 100, true),
('Denim Jeans', 'Classic denim jeans with perfect fit', 79.99, 'https://images.unsplash.com/photo-1542272604-787c3835535d?w=500', (SELECT id FROM categories WHERE name = 'Clothing'), 75, false),
('Winter Jacket', 'Warm winter jacket for cold weather', 149.99, 'https://images.unsplash.com/photo-1544966503-7cc5ac882d5f?w=500', (SELECT id FROM categories WHERE name = 'Clothing'), 25, false),
('Running Shoes', 'Comfortable running shoes for athletes', 119.99, 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=500', (SELECT id FROM categories WHERE name = 'Clothing'), 60, true),

-- Home & Garden
('Coffee Maker', 'Automatic coffee maker with timer', 89.99, 'https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?w=500', (SELECT id FROM categories WHERE name = 'Home & Garden'), 35, false),
('Plant Pot Set', 'Set of decorative plant pots', 39.99, 'https://images.unsplash.com/photo-1485955900006-10f4d324d411?w=500', (SELECT id FROM categories WHERE name = 'Home & Garden'), 80, false),
('LED Desk Lamp', 'Adjustable LED desk lamp with USB charging', 59.99, 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=500', (SELECT id FROM categories WHERE name = 'Home & Garden'), 45, false),

-- Sports
('Yoga Mat', 'Non-slip yoga mat for exercise', 34.99, 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=500', (SELECT id FROM categories WHERE name = 'Sports'), 90, false),
('Dumbbells Set', 'Adjustable dumbbells for home workout', 199.99, 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=500', (SELECT id FROM categories WHERE name = 'Sports'), 15, true),
('Tennis Racket', 'Professional tennis racket', 129.99, 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=500', (SELECT id FROM categories WHERE name = 'Sports'), 25, false),

-- Books
('Programming Guide', 'Complete guide to modern programming', 49.99, 'https://images.unsplash.com/photo-1532012197267-da84d127e765?w=500', (SELECT id FROM categories WHERE name = 'Books'), 70, false),
('Cookbook', 'Delicious recipes for home cooking', 24.99, 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=500', (SELECT id FROM categories WHERE name = 'Books'), 55, false);

-- Insert sample customers
INSERT INTO customers (email, first_name, last_name, phone, address, city, postal_code, country) VALUES
('<EMAIL>', 'John', 'Doe', '+1234567890', '123 Main St', 'New York', '10001', 'USA'),
('<EMAIL>', 'Jane', 'Smith', '+1987654321', '456 Oak Ave', 'Los Angeles', '90210', 'USA'),
('<EMAIL>', 'Bob', 'Johnson', '+1122334455', '789 Pine Rd', 'Chicago', '60601', 'USA');

-- Insert sample admin users
INSERT INTO admin_users (email, role) VALUES
('<EMAIL>', 'admin'),
('<EMAIL>', 'admin');
