#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🚀 Setting up E-commerce Store with Stripe Integration...\n');

// Check if .env files exist
const clientEnvPath = path.join(__dirname, '.env');
const serverEnvPath = path.join(__dirname, 'server', '.env');

// Create client .env if it doesn't exist
if (!fs.existsSync(clientEnvPath)) {
  console.log('📝 Creating client .env file...');
  const clientEnvContent = `# Client Environment Variables
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key_here
VITE_API_BASE_URL=http://localhost:3001/api
`;
  fs.writeFileSync(clientEnvPath, clientEnvContent);
  console.log('✅ Client .env file created');
} else {
  console.log('✅ Client .env file already exists');
}

// Create server .env if it doesn't exist
if (!fs.existsSync(serverEnvPath)) {
  console.log('📝 Creating server .env file...');
  const serverEnvContent = `# Server Environment Variables
NODE_ENV=development
PORT=3001
FRONTEND_URL=http://localhost:5173
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
`;
  fs.writeFileSync(serverEnvPath, serverEnvContent);
  console.log('✅ Server .env file created');
} else {
  console.log('✅ Server .env file already exists');
}

console.log('\n📋 Next Steps:');
console.log('1. Edit .env files with your actual credentials:');
console.log('   - Client: .env');
console.log('   - Server: server/.env');
console.log('');
console.log('2. Get your Stripe keys from: https://dashboard.stripe.com/apikeys');
console.log('');
console.log('3. Get your Supabase credentials from: https://supabase.com/dashboard');
console.log('');
console.log('4. Run the database migration:');
console.log('   - Go to your Supabase SQL Editor');
console.log('   - Run: database/migrations/015_remove_stripe_integration.sql');
console.log('');
console.log('5. Start both client and server:');
console.log('   npm run dev:all');
console.log('');
console.log('6. Or start them separately:');
console.log('   - Client: npm run dev:client');
console.log('   - Server: npm run dev:server');
console.log('');
console.log('🎉 Setup complete! Happy coding!'); 