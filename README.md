# E-Commerce Store with Admin Panel

A full-stack e-commerce application built with React, TypeScript, Tailwind CSS, and Supabase.

## Features

### Public Website
- **Homepage** with hero section, featured products, and category grid
- **Products page** with filtering, sorting, and search
- **Product detail pages** with full product information
- **Shopping cart** and checkout functionality
- **Stripe payment processing** (standalone integration)
- **Responsive design** for all devices

### Admin Panel
- **Dashboard** with analytics and recent activity
- **Product management** (CRUD operations)
- **Category management** 
- **Order management** and tracking
- **Customer management**
- **Authentication** with role-based access
- **Real-time data** from Supabase

### Technical Features
- **TypeScript** for type safety
- **Supabase** for database and authentication
- **Stripe** for payment processing (standalone API integration)
- **Real-time updates** and data synchronization
- **Protected routes** for admin access
- **Error handling** and loading states
- **Responsive UI** with Tailwind CSS

## Setup Instructions

### 1. Prerequisites
- Node.js (v16 or higher)
- npm or yarn
- Supabase account
- Stripe account
- Your own API server for Stripe operations

### 2. <PERSON>lone and Install
```bash
git clone <repository-url>
cd STORE
npm install
```

### 3. Supabase Setup

#### Create a Supabase Project
1. Go to [Supabase](https://supabase.com)
2. Create a new project
3. Wait for the project to be fully set up

#### Get Your Credentials
1. In your Supabase project dashboard, go to **Settings** > **API**
2. Copy the following values:
   - **Project URL** (something like `https://your-project-id.supabase.co`)
   - **anon public** key (the public anonymous key)

#### Update Environment Variables
1. Copy the `.env` file in the root directory
2. Replace the placeholder values with your actual credentials:

```env
# Supabase Configuration
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your-actual-anon-key-here

# Stripe Configuration
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key

# Your API Configuration
VITE_API_BASE_URL=https://your-api-domain.com/api
```

#### Run Database Migrations
1. In your Supabase project dashboard, go to **SQL Editor**
2. Copy and paste the contents of `database/migrations/001_initial_schema.sql`
3. Click **Run** to create all the tables and indexes
4. Copy and paste the contents of `database/migrations/002_sample_data.sql`
5. Click **Run** to insert sample data
6. Copy and paste the contents of `database/migrations/015_remove_stripe_integration.sql`
7. Click **Run** to clean up any existing Stripe tables

#### Set Up Authentication
1. In Supabase dashboard, go to **Authentication** > **Users**
2. Create a new user with email and password
3. Go to **SQL Editor** and run:
```sql
INSERT INTO admin_users (email, role) VALUES ('<EMAIL>', 'super_admin');
```

### 4. Stripe Setup

#### Get Your Stripe Keys
1. Log in to your [Stripe Dashboard](https://dashboard.stripe.com)
2. Go to **Developers** > **API keys**
3. Copy your **Publishable key** and **Secret key**
4. For testing, use the test keys (they start with `pk_test_` and `sk_test_`)

#### Set Up Your API Server
You need to set up your own API server to handle Stripe operations. See `STANDALONE_STRIPE_SETUP.md` for detailed instructions on implementing the required endpoints:

- `POST /api/stripe/create-payment-intent`
- `POST /api/stripe/confirm-payment`
- `POST /api/stripe/create-customer`
- `POST /api/stripe/save-payment-method`
- `GET /api/stripe/payment-methods/:customerId`
- `DELETE /api/stripe/payment-methods/:paymentMethodId`
- `GET /api/stripe/payment-history/:customerId`
- `POST /api/stripe/webhook`

### 5. Start Development Server
```bash
npm run dev
```

The application will be available at `http://localhost:5173`

## Usage

### Public Website
- Visit `http://localhost:5173` to see the homepage
- Browse products, categories, and use the shopping cart
- All data is loaded from your Supabase database
- Payments are processed through your API server

### Admin Panel
- Visit `http://localhost:5173/admin/login`
- Login with the admin credentials you created
- Access the dashboard and manage your store

## Project Structure

```
src/
├── components/          # Reusable UI components
├── contexts/           # React contexts (Auth, Cart)
├── lib/               # Utilities and configurations
├── pages/             # Page components
├── services/          # API service layer
└── types/             # TypeScript type definitions

database/
├── migrations/        # SQL migration files
└── README.md         # Database setup instructions
```

## API Services

The application uses a service layer pattern for all database operations:

- **ProductService** - Product CRUD operations
- **CategoryService** - Category management
- **OrderService** - Order processing
- **CustomerService** - Customer management
- **AuthService** - Authentication and authorization
- **DashboardService** - Analytics and reporting
- **StandaloneStripeService** - Payment processing (via your API)

## Database Schema

### Tables
- **categories** - Product categories
- **products** - Store products with category relationships
- **customers** - Customer information
- **orders** - Customer orders with payment status
- **order_items** - Individual items within orders
- **admin_users** - Admin panel users

### Key Features
- UUID primary keys for all tables
- Proper foreign key relationships
- Check constraints for data validation
- Automatic timestamps with triggers
- Indexes for performance optimization
- Basic payment status tracking in orders table

## Payment Processing

The application uses a standalone Stripe integration that communicates with your own API server:

1. **Frontend** creates payment intents through your API
2. **Your API** communicates with Stripe using the secret key
3. **Stripe** processes payments and sends webhooks to your API
4. **Your API** updates order status based on webhook events

This approach provides:
- ✅ **Security**: Secret keys never exposed to the client
- ✅ **Flexibility**: Full control over payment logic
- ✅ **Scalability**: Can handle complex payment workflows
- ✅ **Compliance**: Meets PCI requirements

## Troubleshooting

### Common Issues

1. **Connection Error**: Make sure your environment variables are correct
2. **CORS Error**: Ensure your domain is added to the allowed origins in Supabase settings
3. **Permission Error**: Check that RLS policies allow the operations you're trying to perform
4. **Build Error**: Make sure all dependencies are installed with `npm install`
5. **Payment Error**: Verify your API server is running and Stripe keys are correct

### Getting Help
- Check the browser console for detailed error messages
- Verify your API keys and project URL are correct
- Review the Supabase documentation: https://supabase.com/docs
- Review the Stripe documentation: https://stripe.com/docs
- Check `STANDALONE_STRIPE_SETUP.md` for detailed Stripe setup instructions

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.
