import React, { useEffect, useState } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import ProtectedRoute from './components/ProtectedRoute';
import Header from './components/Header';
import Footer from './components/Footer';
import ScrollToTop from './components/ScrollToTop';
import Toast from './components/Toast';
import { CartProvider, useCart } from './contexts/CartContext';
import HomePage from './pages/HomePage';
import ProductsPage from './pages/ProductsPage';
import ProductDetailPage from './pages/ProductDetailPage';
import CartPage from './pages/CartPage';
import CheckoutPage from './pages/CheckoutPage';
import PaymentSuccessPage from './pages/PaymentSuccessPage';
import PaymentFailurePage from './pages/PaymentFailurePage';
import PaymentCancelledPage from './pages/PaymentCancelledPage';
import PaymentStatusDemoPage from './pages/PaymentStatusDemoPage';
import AdminLoginPage from './pages/AdminLoginPage';
import AdminDashboardPage from './pages/AdminDashboardPage';
import AdminProductsPage from './pages/AdminProductsPage';
import AdminCategoriesPage from './pages/AdminCategoriesPage';
import AdminOrdersPage from './pages/AdminOrdersPage';
import AdminCustomersPage from './pages/AdminCustomersPage';
import AdminSettingsPage from './pages/AdminSettingsPage';
import CategoriesPage from './pages/CategoriesPage';
import { supabase } from './lib/supabase';

const AppContent = () => {
  const { toast, hideToast } = useCart();
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setUser(session?.user ?? null);
      setLoading(false);
    });

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((_event, session) => {
      setUser(session?.user ?? null);
    });

    return () => subscription.unsubscribe();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      <Router>
        <ScrollToTop />
        <Routes>
          {/* Admin routes - no header/footer */}
          <Route
            path="/admin/login"
            element={<AdminLoginPage />}
          />
          <Route
            path="/admin"
            element={<AdminDashboardPage />}
          />
          <Route
            path="/admin/products"
            element={<AdminProductsPage />}
          />
          <Route
            path="/admin/categories"
            element={<AdminCategoriesPage />}
          />
          <Route
            path="/admin/orders"
            element={<AdminOrdersPage />}
          />
          <Route
            path="/admin/customers"
            element={<AdminCustomersPage />}
          />
          <Route
            path="/admin/settings"
            element={<AdminSettingsPage />}
          />
        
        {/* Public routes - with header/footer */}
        <Route path="/" element={
          <>
            <Header />
            <HomePage />
            <Footer />
          </>
        } />
        <Route path="/products" element={
          <>
            <Header />
            <ProductsPage />
            <Footer />
          </>
        } />
        <Route path="/products/:id" element={
          <>
            <Header />
            <ProductDetailPage />
            <Footer />
          </>
        } />
        <Route path="/cart" element={
          <>
            <Header />
            <CartPage />
            <Footer />
          </>
        } />
        <Route path="/checkout" element={
          <>
            <Header />
            <CheckoutPage />
            <Footer />
          </>
        } />
        <Route path="/payment-success" element={
          <>
            <Header />
            <PaymentSuccessPage />
            <Footer />
          </>
        } />
        <Route path="/payment-failure" element={
          <>
            <Header />
            <PaymentFailurePage />
            <Footer />
          </>
        } />
        <Route path="/payment-cancelled" element={
          <>
            <Header />
            <PaymentCancelledPage />
            <Footer />
          </>
        } />
        <Route path="/payment-status-demo" element={
          <>
            <Header />
            <PaymentStatusDemoPage />
            <Footer />
          </>
        } />
        <Route path="/categories" element={
          <>
            <Header />
            <CategoriesPage />
            <Footer />
          </>
        } />
          <Route path="*" element={
            <>
              <Header />
              <div className="p-12 text-center text-2xl">404 - Page Not Found</div>
              <Footer />
            </>
          } />
        </Routes>
      </Router>
      
      {/* Toast Notification */}
      <Toast
        message={toast.message}
        type={toast.type}
        isVisible={toast.isVisible}
        onClose={hideToast}
      />
    </div>
  );
};

function App() {
  return (
    <CartProvider>
      <AppContent />
    </CartProvider>
  );
}

export default App;