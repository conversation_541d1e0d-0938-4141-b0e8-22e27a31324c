# Guest Checkout Setup

This document explains the guest checkout feature that allows customers to complete purchases without creating an account.

## Features

- **Guest Checkout**: Customers can complete purchases without authentication
- **Temporary Stripe Customers**: Guest orders create temporary Stripe customers for payment processing
- **Order Tracking**: Guest orders are flagged with `is_guest_order = true` for easy identification
- **Payment Processing**: Full Stripe integration for guest payments

## Database Changes

The following database changes are required:

1. **Orders Table**:
   - `customer_id` can now be NULL for guest orders
   - Added `is_guest_order` boolean flag
   - Added `payment_status` field

2. **Payments Table** (new):
   - Stores payment intent information
   - Supports both authenticated and guest customers
   - Links to orders and tracks payment status

## Migration

Run the migration to enable guest checkout:

```sql
-- Run the guest checkout migration
\i database/migrations/010_add_guest_checkout.sql
```

## How It Works

1. **Checkout Flow**:
   - User proceeds to checkout without authentication
   - System creates order with `customer_id = NULL` and `is_guest_order = true`
   - Temporary Stripe customer is created for payment processing
   - Payment intent is created and stored in payments table

2. **Payment Processing**:
   - <PERSON><PERSON> handles payment with temporary customer
   - Webhook updates order and payment status
   - Guest orders are processed the same as authenticated orders

3. **Order Management**:
   - Guest orders can be identified by `is_guest_order = true`
   - Orders can be linked to customers later if they create accounts
   - Full order history is maintained

## Configuration

Ensure your Stripe configuration is set up:

1. **Environment Variables**:
   ```
   VITE_STRIPE_PUBLISHABLE_KEY=pk_test_...
   STRIPE_SECRET_KEY=sk_test_...
   STRIPE_WEBHOOK_SECRET=whsec_...
   ```

2. **Supabase Functions**:
   - `create-payment-intent`: Handles guest customer creation
   - `stripe-webhook`: Processes payment events for guest orders

## Benefits

- **Reduced Friction**: Customers can purchase without account creation
- **Higher Conversion**: Eliminates barriers to purchase
- **Flexible**: Supports both guest and authenticated checkout
- **Trackable**: Full order and payment tracking for guest orders

## Future Enhancements

- Email collection for guest orders
- Account creation after guest purchase
- Guest order history lookup
- Marketing integration for guest customers 