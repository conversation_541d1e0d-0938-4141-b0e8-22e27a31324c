import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import PaymentStatusComponent from '../components/PaymentStatusComponent';

const PaymentStatusDemoPage = () => {
  const navigate = useNavigate();
  const [currentStatus, setCurrentStatus] = useState<'success' | 'failed' | 'pending' | 'cancelled'>('success');

  // Mock data for demonstration
  const mockPaymentIntent = {
    id: 'pi_3OqX8X2eZvKYlo2C1gQ12345',
    status: 'succeeded',
    amount: 2999,
    currency: 'usd',
    created: Math.floor(Date.now() / 1000),
    payment_method: {
      type: 'card',
      card: {
        last4: '4242',
        brand: 'visa'
      }
    }
  };

  const mockOrderItems = [
    {
      id: '1',
      name: 'Premium Wireless Headphones',
      price: 199.99,
      quantity: 1
    },
    {
      id: '2',
      name: 'Bluetooth Speaker',
      price: 99.99,
      quantity: 1
    }
  ];

  const mockErrorMessages = {
    card_declined: 'Your card was declined. Please try a different payment method.',
    insufficient_funds: 'Your account doesn\'t have enough funds to complete this transaction.',
    expired_card: 'The card you\'re using has expired.',
    invalid_cvc: 'The security code (CVC) you entered is incorrect.',
    network_error: 'We encountered a network issue while processing your payment.',
    generic: 'Payment processing failed. Please try again.'
  };

  const handleRetry = () => {
    alert('Retry payment clicked!');
  };

  const handleBack = () => {
    navigate('/');
  };

  const getCurrentError = () => {
    switch (currentStatus) {
      case 'failed':
        return mockErrorMessages.card_declined;
      default:
        return '';
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 font-['Plus_Jakarta_Sans']">
      {/* Demo Controls */}
      <div className="bg-white shadow-sm border-b p-4">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">Payment Status Component Demo</h1>
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => setCurrentStatus('success')}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                currentStatus === 'success' 
                  ? 'bg-green-600 text-white' 
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Success
            </button>
            <button
              onClick={() => setCurrentStatus('failed')}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                currentStatus === 'failed' 
                  ? 'bg-red-600 text-white' 
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Failed
            </button>
            <button
              onClick={() => setCurrentStatus('cancelled')}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                currentStatus === 'cancelled' 
                  ? 'bg-yellow-600 text-white' 
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Cancelled
            </button>
            <button
              onClick={() => setCurrentStatus('pending')}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                currentStatus === 'pending' 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Pending
            </button>
          </div>
          <p className="text-sm text-gray-600 mt-2">
            Click the buttons above to see different payment status states
          </p>
        </div>
      </div>

      {/* Payment Status Component */}
      <PaymentStatusComponent
        status={currentStatus}
        paymentIntent={currentStatus === 'success' || currentStatus === 'failed' ? mockPaymentIntent : undefined}
        orderId="ORD-2024-001234"
        total={299.98}
        orderItems={mockOrderItems}
        errorMessage={getCurrentError()}
        onRetry={handleRetry}
        onBack={handleBack}
      />
    </div>
  );
};

export default PaymentStatusDemoPage; 