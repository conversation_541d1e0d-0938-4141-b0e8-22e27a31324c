import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import PaymentStatusComponent from '../components/PaymentStatusComponent';

const PaymentFailurePage = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { 
    paymentIntent, 
    orderId, 
    total, 
    orderItems, 
    errorMessage = 'Payment processing failed. Please try again.' 
  } = location.state || {};

  const handleRetry = () => {
    // Navigate back to checkout to retry payment
    navigate('/checkout', { 
      state: { 
        cartItems: orderItems,
        total: total
      } 
    });
  };

  const handleBack = () => {
    navigate('/cart');
  };

  return (
    <PaymentStatusComponent
      status="failed"
      paymentIntent={paymentIntent}
      orderId={orderId}
      total={total}
      orderItems={orderItems}
      errorMessage={errorMessage}
      onRetry={handleRetry}
      onBack={handleBack}
    />
  );
};

export default PaymentFailurePage; 