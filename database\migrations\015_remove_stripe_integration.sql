-- Migration: Remove Stripe integration tables and columns
-- This migration removes all Stripe-related database structures

-- Drop Stripe-related tables
DROP TABLE IF EXISTS webhook_events CASCADE;
DROP TABLE IF EXISTS payment_methods CASCADE;
DROP TABLE IF EXISTS payments CASCADE;

-- Remove Stripe-related columns from orders table
ALTER TABLE orders DROP COLUMN IF EXISTS stripe_payment_intent_id;
ALTER TABLE orders DROP COLUMN IF EXISTS payment_status;

-- Drop any Stripe-related functions
DROP FUNCTION IF EXISTS create_payment_intent_local(INTEGER, UUID, UUID, TEXT, JSONB);
DROP FUNCTION IF EXISTS update_payment_status(TEXT, TEXT, TEXT);
DROP FUNCTION IF EXISTS save_payment_method_local(UUID, TEXT, TEXT, TEXT, TEXT, INTEGER, INTEGER, BOOLEAN);

-- Remove Stripe-related indexes
DROP INDEX IF EXISTS idx_payments_stripe_payment_intent_id;
DROP INDEX IF EXISTS idx_payments_order_id;
DROP INDEX IF EXISTS idx_payments_customer_id;
DROP INDEX IF EXISTS idx_payments_status;
DROP INDEX IF EXISTS idx_payment_methods_customer_id;
DROP INDEX IF EXISTS idx_payment_methods_stripe_payment_method_id;
DROP INDEX IF EXISTS idx_webhook_events_stripe_event_id;
DROP INDEX IF EXISTS idx_webhook_events_event_type;
DROP INDEX IF EXISTS idx_webhook_events_processed;

-- Clean up any remaining Stripe-related policies
DROP POLICY IF EXISTS "Users can create their own payment intents" ON payments;
DROP POLICY IF EXISTS "Users can update their own payments" ON payments;
DROP POLICY IF EXISTS "Customers can view their own payment methods" ON payment_methods;
DROP POLICY IF EXISTS "Customers can insert their own payment methods" ON payment_methods;
DROP POLICY IF EXISTS "Customers can update their own payment methods" ON payment_methods;
DROP POLICY IF EXISTS "Customers can delete their own payment methods" ON payment_methods;
DROP POLICY IF EXISTS "Admins can view all payment methods" ON payment_methods;
DROP POLICY IF EXISTS "Admins can manage all payment methods" ON payment_methods;
DROP POLICY IF EXISTS "Admins can view all webhook events" ON webhook_events;
DROP POLICY IF EXISTS "Admins can manage all webhook events" ON webhook_events;

-- Add a simple payment_status column to orders for basic payment tracking
ALTER TABLE orders ADD COLUMN IF NOT EXISTS payment_status TEXT DEFAULT 'pending';

-- Add comment to explain the new payment_status column
COMMENT ON COLUMN orders.payment_status IS 'Basic payment status: pending, paid, failed, refunded'; 